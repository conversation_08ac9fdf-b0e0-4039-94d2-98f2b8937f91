---
type: "agent_requested"
description: "Example description"
---
# Rules – Frontend‑Only Development Stage

## 1 · One Source of Truth
- Each global concern has a single store/composable:
  - Auth → /stores/auth.ts
  - Products → /stores/products.ts
  - Wallet → /stores/wallet.ts
  - Caching → /composables/useCache.ts
- Never duplicate logic in components.
- If logic already exists, extend it instead of creating new files.

## 2 · File Lifecycle
- Update existing files instead of creating new ones.
- Delete unused or replaced files immediately (no *-copy.vue).
- After each task, update /README.md → “Changelog” section.

## 3 · Global State & Caching
- All fetches go through composables (not inside pages/components).
- Caching is centralized in /useCache.ts.
- Use timestamps (created_at, updated_at) for invalidation.
- No feature may have its own private cache.

## 4 · Security & Role Logic (Stub Stage)
- Role logic lives in /stores/auth.ts and /middleware/roleGuard.ts.
- Pages requiring auth must use global middleware, not ad-hoc checks.
- For now, fake/mock role checks can be used, but must be ready to plug into backend later.

## 5 · Performance & Cleanup
- Prevent duplicate fetch loops by always reusing stores/composables.
- Avoid background timers or polling unless globally managed.
- Delete any experimental/test components once replaced.

## 6 · Error Handling
- Create /composables/useError.ts for all error handling.
- No inline try/catch inside components unless calling global handler.
- UI must degrade gracefully if fetch fails (show fallback, not crash).

✅ Goal: Keep the frontend super simple, modular, and clean while mocking backend logic. 
When backend starts, this rules file will be extended for DB & API security.