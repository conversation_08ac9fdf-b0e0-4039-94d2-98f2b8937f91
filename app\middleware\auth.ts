export default defineNuxtRouteMiddleware((to, from) => {
  const { isAuthenticated, initializeAuth } = useAuthStore();

  // Initialize auth state from localStorage
  if (process.client) {
    initializeAuth();
  }

  // If not authenticated, redirect to home with login prompt and redirect parameter
  if (!isAuthenticated) {
    const redirectPath = to.fullPath;
    return navigateTo(`/?login=required&redirect=${encodeURIComponent(redirectPath)}`);
  }
});
