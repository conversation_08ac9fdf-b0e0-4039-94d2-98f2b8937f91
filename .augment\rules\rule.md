---
type: "always_apply"
---

🔐 Rules Add‑On — Frontend + Backend Together
1 · Unified Source of Truth
Every global concern has one system only:

Auth → /stores/auth.ts + /backend/api/auth/*

Caching → /composables/useCache.ts

Products → /stores/products.ts + /backend/api/products/*

Wallet → /stores/wallet.ts + /backend/api/wallet/*

Never build “local” versions inside components.
❌ Don’t add caching inside a component → ✅ extend /useCache.ts.

2 · File Lifecycle Discipline
When adding a feature, first check if a file already exists to host the logic.

If extending, update existing files.

If replacing, delete the old file — no *-copy.vue or old-*.ts.

After finishing a task, update /README.md under “Changelog” with ✔️ file updated/removed.

3 · Backend & Frontend Coupling
For every frontend store there is a matching backend API folder:

bash
Copy
Edit
/stores/products.ts      ↔   /backend/api/products.ts
/stores/orders.ts        ↔   /backend/api/orders.ts
/stores/wallet.ts        ↔   /backend/api/wallet.ts
Do not create free‑floating APIs or stores.

Keep backend APIs in /backend/ only.

No logic duplication: the backend provides data, the frontend consumes it.

4 · Global Security Model
Role/permission checks live in one place only:

/stores/auth.ts → client role checks

/middleware/roleGuard.ts → navigation guards

/backend/services/auth.ts → backend role enforcement

No ad‑hoc role checks inside pages/components.

5 · Caching & State Management
All caching uses /useCache.ts.

Cache invalidation uses timestamps (created_at, updated_at).

If a component fetches data, it must call a composable that uses global cache — not direct $fetch.

If caching system already exists, never build a new one per feature.

6 · API Design & Security
Backend API routes must:

Derive tenant_id server‑side (never trust client).

Check role before executing.

Live in /backend/api/ — not in /pages/ or /components/.

No duplicate or shadow endpoints (e.g. don’t add /api/products-filtered.ts if /api/products.ts exists). Extend the existing one.

7 · Performance & Cleanup
Prevent background duplication:

No multiple fetch loops for the same resource.

If useProducts.ts already subscribes to product updates, reuse it everywhere.

Delete unused APIs, stores, or composables as soon as replaced.

Avoid “patches” that create overlapping logic.

8 · Changelog Discipline
After each PR/task:

Update /README.md → “Changelog” section.

List each file touched with status:

✔️ Updated

❌ Removed

➕ Added

This keeps track of what exists — avoiding “forgotten files”.

✅ Goal: Build frontend + backend together with zero duplicate systems, zero leftover files, one source of truth for each concern, and consistent global security & caching.