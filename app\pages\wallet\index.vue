<template>
  <div class="min-h-screen bg-theme-background" dir="rtl">
    <!-- Mobile Sticky Header -->
    <div class="sticky top-0 z-40 bg-theme-glass backdrop-blur-md border-b border-theme-light p-4 sm:hidden">
      <div class="flex items-center justify-between">
        <h1 class="section-title text-xl">المحفظة</h1>
        <div class="flex items-center space-x-2 space-x-reverse">
          <button @click="toggleCurrency" class="p-2 rounded-xl bg-theme-surface/20 hover:bg-theme-surface/40 transition-all duration-200">
            <span class="text-white-force text-sm font-medium">{{ currentCurrency }}</span>
          </button>
          <button @click="refreshWallet" class="p-2 rounded-xl bg-theme-surface/20 hover:bg-theme-surface/40 transition-all duration-200">
            <svg class="w-5 h-5 text-white-force" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <div class="container mx-auto px-4 py-6 sm:py-8">
      <!-- Desktop Header -->
      <div class="hidden sm:flex items-center justify-between mb-8">
        <div>
          <h1 class="section-title text-3xl mb-2">المحفظة</h1>
          <p class="text-theme-secondary">إدارة رصيدك ومعاملاتك المالية</p>
        </div>
        <div class="flex items-center space-x-3 space-x-reverse">
          <button @click="exportTransactions" class="btn-secondary flex items-center space-x-2 space-x-reverse">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <span>تحميل كشف الحساب</span>
          </button>
          <button @click="toggleCurrency" class="btn-secondary flex items-center space-x-2 space-x-reverse">
            <span>{{ currentCurrency === 'SAR' ? 'USD' : 'SAR' }}</span>
          </button>
          <button @click="refreshWallet" class="btn-secondary flex items-center space-x-2 space-x-reverse">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span>تحديث</span>
          </button>
        </div>
      </div>

      <!-- Balance Card -->
      <div class="bg-gradient-to-br from-purple-600/20 to-pink-600/20 backdrop-blur-md border border-theme-light rounded-3xl p-6 relative overflow-hidden shadow-2xl mb-8">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-600/10 to-pink-600/10"></div>

        <div class="relative z-10">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="text-center sm:text-right mb-6 sm:mb-0">
              <div class="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mx-auto sm:mx-0 mb-4 shadow-lg">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <h2 class="text-white-force text-lg font-bold mb-2">الرصيد الإجمالي</h2>
              <div class="text-4xl sm:text-5xl font-bold text-white-force mb-1">{{ formattedBalance.amount }}</div>
              <div class="text-theme-muted text-base">{{ formattedBalance.symbol }}</div>
            </div>

            <!-- Quick Action Buttons -->
            <div class="grid grid-cols-2 gap-3 sm:flex sm:space-x-3 sm:space-x-reverse">
              <button @click="showDepositModal = true" class="btn-primary flex items-center justify-center space-x-2 space-x-reverse py-3 px-6 font-bold hover:scale-105 transition-all duration-200">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                <span>إيداع</span>
              </button>
              <button @click="showWithdrawModal = true" class="btn-secondary flex items-center justify-center space-x-2 space-x-reverse py-3 px-6 font-bold hover:scale-105 transition-all duration-200">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                </svg>
                <span>سحب</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Stats Section - 4 Cards Grid -->
      <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-8">
        <!-- Total Balance -->
        <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-4 text-center">
          <div class="w-12 h-12 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center mx-auto mb-3">
            <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
          <div class="text-lg sm:text-xl font-bold text-white-force mb-1">{{ stats.total_balance.toLocaleString() }}</div>
          <div class="text-theme-muted text-xs sm:text-sm">الرصيد الإجمالي</div>
        </div>

        <!-- Total Deposits -->
        <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-4 text-center">
          <div class="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center mx-auto mb-3">
            <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
          <div class="text-lg sm:text-xl font-bold text-green-400 mb-1">{{ stats.total_deposits.toLocaleString() }}</div>
          <div class="text-theme-muted text-xs sm:text-sm">إجمالي الإيداعات</div>
        </div>

        <!-- Total Expenses -->
        <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-4 text-center">
          <div class="w-12 h-12 bg-red-500/20 rounded-xl flex items-center justify-center mx-auto mb-3">
            <svg class="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
            </svg>
          </div>
          <div class="text-lg sm:text-xl font-bold text-red-400 mb-1">{{ stats.total_expenses.toLocaleString() }}</div>
          <div class="text-theme-muted text-xs sm:text-sm">إجمالي المصروفات</div>
        </div>

        <!-- Transaction Count -->
        <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-4 text-center">
          <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mx-auto mb-3">
            <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <div class="text-lg sm:text-xl font-bold text-blue-400 mb-1">{{ stats.transaction_count }}</div>
          <div class="text-theme-muted text-xs sm:text-sm">عدد المعاملات</div>
        </div>
      </div>

      <!-- Transaction Filters -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-4 mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div class="flex items-center space-x-4 space-x-reverse">
            <h3 class="text-white-force font-bold">تصفية المعاملات:</h3>
            <div class="flex bg-theme-surface/20 rounded-xl p-1">
              <button
                @click="setTransactionFilter('all')"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                  transactionFilter === 'all'
                    ? 'bg-theme-accent text-white shadow-lg'
                    : 'text-theme-muted hover:text-white-force'
                ]"
              >
                الكل
              </button>
              <button
                @click="setTransactionFilter('deposits')"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                  transactionFilter === 'deposits'
                    ? 'bg-green-500 text-white shadow-lg'
                    : 'text-theme-muted hover:text-white-force'
                ]"
              >
                الإيداعات
              </button>
              <button
                @click="setTransactionFilter('expenses')"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                  transactionFilter === 'expenses'
                    ? 'bg-red-500 text-white shadow-lg'
                    : 'text-theme-muted hover:text-white-force'
                ]"
              >
                المصروفات
              </button>
            </div>
          </div>

          <button @click="exportTransactions" class="btn-secondary flex items-center space-x-2 space-x-reverse sm:hidden">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <span>تحميل كشف الحساب</span>
          </button>
        </div>
      </div>

      <!-- Transactions List -->
      <div class="bg-theme-glass backdrop-blur-md border border-theme-light rounded-3xl p-6 shadow-xl">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-white-force text-xl font-bold">المعاملات</h2>
          <div class="text-theme-muted text-sm">
            {{ filteredTransactions.length }} من {{ transactions.length }} معاملة
          </div>
        </div>

        <div v-if="isLoading" class="text-center py-12">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-theme-accent"></div>
          <p class="mt-2 text-theme-muted">جاري التحميل...</p>
        </div>

        <div v-else-if="paginatedTransactions.length > 0" class="space-y-3">
          <div v-for="transaction in paginatedTransactions" :key="transaction.id"
               class="group flex items-center justify-between p-4 bg-theme-surface/20 rounded-2xl hover:bg-theme-surface/40 transition-all duration-200 hover:scale-[1.02]">
            <div class="flex items-center space-x-3 space-x-reverse">
              <div :class="[
                'w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-200',
                transaction.type === 'deposit' ? 'bg-green-500/20' : 'bg-red-500/20'
              ]">
                <span class="text-lg">{{ getTransactionIcon(transaction.type) }}</span>
              </div>
              <div>
                <div class="text-white-force font-semibold text-base">{{ transaction.title }}</div>
                <div class="text-theme-muted text-sm">{{ formatTransactionDate(transaction.created_at) }}</div>
                <div v-if="transaction.description" class="text-theme-muted text-xs mt-1">{{ transaction.description }}</div>
              </div>
            </div>
            <div class="text-left">
              <div :class="[
                'font-bold text-lg mb-1',
                getAmountClass(transaction.type)
              ]">
                {{ transaction.type === 'deposit' ? '+' : '-' }}{{ transaction.amount.toLocaleString() }} ر.س
              </div>
              <div :class="[
                'text-xs px-2 py-1 rounded-full border',
                getStatusBadgeClass(transaction.status)
              ]">
                {{
                  transaction.status === 'completed' ? 'مكتمل' :
                  transaction.status === 'pending' ? 'قيد التنفيذ' : 'مرفوض'
                }}
              </div>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-12">
          <svg class="w-16 h-16 text-theme-muted mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
          </svg>
          <h3 class="text-white-force text-lg font-semibold mb-2">لا توجد معاملات</h3>
          <p class="text-theme-muted">
            {{ transactionFilter === 'all' ? 'ابدأ بإجراء أول معاملة لك' : 'لا توجد معاملات تطابق الفلتر المحدد' }}
          </p>
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex items-center justify-center mt-6 space-x-2 space-x-reverse">
          <button
            @click="setPage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="p-2 rounded-lg bg-theme-surface/20 hover:bg-theme-surface/40 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            <svg class="w-5 h-5 text-white-force" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>

          <div class="flex items-center space-x-1 space-x-reverse">
            <button
              v-for="page in Math.min(totalPages, 5)"
              :key="page"
              @click="setPage(page)"
              :class="[
                'w-10 h-10 rounded-lg font-medium transition-all duration-200',
                currentPage === page
                  ? 'bg-theme-accent text-white shadow-lg'
                  : 'bg-theme-surface/20 text-theme-muted hover:bg-theme-surface/40 hover:text-white-force'
              ]"
            >
              {{ page }}
            </button>
          </div>

          <button
            @click="setPage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="p-2 rounded-lg bg-theme-surface/20 hover:bg-theme-surface/40 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            <svg class="w-5 h-5 text-white-force" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <DepositModal v-if="showDepositModal" @close="showDepositModal = false" />
    <WithdrawModal v-if="showWithdrawModal" @close="showWithdrawModal = false" />
  </div>
</template>

<script setup lang="ts">
// Middleware
definePageMeta({
  middleware: 'auth'
})

// SEO Meta
useSeoMeta({
  title: 'المحفظة - بنتاكون',
  description: 'إدارة رصيدك ومعاملاتك المالية في متجر بنتاكون',
})

// Composables
const {
  balance,
  stats,
  transactions,
  filteredTransactions,
  paginatedTransactions,
  currentCurrency,
  transactionFilter,
  currentPage,
  totalPages,
  isLoading,
  formattedBalance,
  setTransactionFilter,
  toggleCurrency,
  setPage,
  refreshWallet,
  exportTransactions,
  formatTransactionDate
} = useWallet()

// Reactive state
const showDepositModal = ref(false)
const showWithdrawModal = ref(false)

// Methods
const getTransactionIcon = (type: string) => {
  switch (type) {
    case 'deposit': return '💳'
    case 'purchase': return '🛒'
    default: return '💰'
  }
}

const getAmountClass = (type: string) => {
  switch (type) {
    case 'deposit': return 'text-green-400'
    case 'purchase': return 'text-red-400'
    default: return 'text-white-force'
  }
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-500/20 text-green-400 border-green-500/30'
    case 'pending':
      return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
    case 'failed':
      return 'bg-red-500/20 text-red-400 border-red-500/30'
    default:
      return 'bg-theme-surface/20 text-theme-muted border-theme-light'
  }
}

// Initialize wallet data on mount
onMounted(() => {
  refreshWallet()
})
</script>
