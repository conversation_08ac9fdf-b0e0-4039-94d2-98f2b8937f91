import { ref, computed } from 'vue'

export interface ExchangeRates {
  USD_SAR: number
  EUR_SAR: number
  GBP_SAR: number
  updated_at: string
}

export const useExchangeRates = () => {
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // Mock exchange rates (in production, fetch from API)
  const mockRates: ExchangeRates = {
    USD_SAR: 3.75,
    EUR_SAR: 4.10,
    GBP_SAR: 4.75,
    updated_at: new Date().toISOString()
  }

  const rates = ref<ExchangeRates>(mockRates)

  // Computed properties
  const usdToSar = computed(() => rates.value.USD_SAR)
  const sarToUsd = computed(() => 1 / rates.value.USD_SAR)

  // Methods
  const fetchExchangeRates = async () => {
    isLoading.value = true
    error.value = null

    try {
      // TODO: Replace with actual API call
      // const response = await $fetch('/backend/api/exchange-rates')
      
      // For now, use mock data
      await new Promise(resolve => setTimeout(resolve, 300))
      rates.value = mockRates
    } catch (err) {
      error.value = 'فشل في تحميل أسعار الصرف'
      console.error('Exchange rates fetch error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const convertSarToUsd = (amount: number): number => {
    return amount / rates.value.USD_SAR
  }

  const convertUsdToSar = (amount: number): number => {
    return amount * rates.value.USD_SAR
  }

  const formatCurrency = (amount: number, currency: 'SAR' | 'USD'): string => {
    if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}`
    }
    return `${amount.toLocaleString('ar-SA')} ر.س`
  }

  return {
    // State
    rates: readonly(rates),
    isLoading: readonly(isLoading),
    error: readonly(error),
    
    // Computed
    usdToSar,
    sarToUsd,
    
    // Methods
    fetchExchangeRates,
    convertSarToUsd,
    convertUsdToSar,
    formatCurrency
  }
}
