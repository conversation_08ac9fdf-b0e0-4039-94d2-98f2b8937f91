import { defineStore } from 'pinia';

export interface Balance {
  usd: number;
  eur: number;
  btc: number;
}

export interface Transaction {
  id: number;
  userId: number;
  type: 'deposit' | 'purchase' | 'profit' | 'withdrawal';
  amount: number;
  currency: string;
  description: string;
  status: 'pending' | 'completed' | 'failed';
  orderId?: number;
  createdAt: string;
  completedAt: string | null;
}

export interface WalletState {
  balance: Balance;
  transactions: Transaction[];
  isLoading: boolean;
}

export const useWalletStore = defineStore('wallet', {
  state: (): WalletState => ({
    balance: {
      usd: 0,
      eur: 0,
      btc: 0
    },
    transactions: [],
    isLoading: false
  }),

  getters: {
    totalBalanceUSD: (state) => {
      // Mock conversion rates - TODO: backend - Use real exchange rates
      const rates = { eur: 1.1, btc: 45000 };
      return state.balance.usd + 
             (state.balance.eur * rates.eur) + 
             (state.balance.btc * rates.btc);
    },

    recentTransactions: (state) => {
      return state.transactions
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 10);
    },

    pendingTransactions: (state) => {
      return state.transactions.filter(t => t.status === 'pending');
    }
  },

  actions: {
    async fetchBalance() {
      this.isLoading = true;
      try {
        // TODO: backend - Replace with real API call
        const { user } = useAuthStore();
        if (user) {
          // Mock balance based on user role
          const mockBalances = {
            admin: { usd: 5000, eur: 4200, btc: 0.15 },
            worker: { usd: 1200, eur: 1000, btc: 0.05 },
            distributor: { usd: 3500, eur: 2900, btc: 0.12 },
            user: { usd: 250, eur: 210, btc: 0.01 }
          };
          
          this.balance = mockBalances[user.role] || mockBalances.user;
        }
      } catch (error) {
        console.error('Failed to fetch balance:', error);
      } finally {
        this.isLoading = false;
      }
    },

    async fetchTransactions() {
      this.isLoading = true;
      try {
        // TODO: backend - Replace with real API call
        const response = await $fetch('/backend/api/wallet/transactions');
        if (response.success) {
          this.transactions = response.data;
        }
      } catch (error) {
        console.error('Failed to fetch transactions:', error);
      } finally {
        this.isLoading = false;
      }
    },

    async deposit(amount: number, currency: string) {
      // TODO: backend - Implement real deposit logic
      console.log(`Mock deposit: ${amount} ${currency}`);
      
      // Mock success
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update balance
      if (currency in this.balance) {
        (this.balance as any)[currency] += amount;
      }
      
      // Add transaction
      const newTransaction: Transaction = {
        id: Date.now(),
        userId: useAuthStore().user?.id || 0,
        type: 'deposit',
        amount,
        currency,
        description: `${currency.toUpperCase()} deposit`,
        status: 'completed',
        createdAt: new Date().toISOString(),
        completedAt: new Date().toISOString()
      };
      
      this.transactions.unshift(newTransaction);
      
      return { success: true };
    }
  }
});
