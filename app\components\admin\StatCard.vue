<template>
  <div class="stat-card" :class="cardClasses">
    <!-- Icon -->
    <div class="stat-icon" :class="iconClasses">
      <component :is="icon" class="w-6 h-6" />
    </div>

    <!-- Content -->
    <div class="stat-content">
      <div class="stat-label arabic-text">{{ label }}</div>
      <div class="stat-value arabic-text">{{ formattedValue }}</div>
      
      <!-- Growth indicator -->
      <div v-if="growth !== undefined" class="stat-growth" :class="growthClasses">
        <svg class="w-4 h-4" :class="growthIconClasses" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path v-if="growth >= 0" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
        </svg>
        <span class="text-sm font-medium">
          {{ Math.abs(growth).toFixed(1) }}%
        </span>
        <span class="text-xs text-theme-muted arabic-text">{{ growthLabel }}</span>
      </div>

      <!-- Additional info -->
      <div v-if="subtitle" class="stat-subtitle arabic-text">{{ subtitle }}</div>
    </div>

    <!-- Loading overlay -->
    <div v-if="isLoading" class="stat-loading">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-theme-primary"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  label: string;
  value: string | number;
  icon: any;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info';
  growth?: number;
  growthLabel?: string;
  subtitle?: string;
  isLoading?: boolean;
  format?: 'number' | 'currency' | 'percentage' | 'custom';
  currencyCode?: string;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  growthLabel: 'من الشهر الماضي',
  format: 'custom',
  currencyCode: 'USD'
});

const adminStore = useAdminStore();

const cardClasses = computed(() => {
  const variants = {
    default: 'stat-card-default',
    primary: 'stat-card-primary',
    success: 'stat-card-success',
    warning: 'stat-card-warning',
    danger: 'stat-card-danger',
    info: 'stat-card-info'
  };
  return variants[props.variant];
});

const iconClasses = computed(() => {
  const variants = {
    default: 'stat-icon-default',
    primary: 'stat-icon-primary',
    success: 'stat-icon-success',
    warning: 'stat-icon-warning',
    danger: 'stat-icon-danger',
    info: 'stat-icon-info'
  };
  return variants[props.variant];
});

const growthClasses = computed(() => {
  if (props.growth === undefined) return '';
  return props.growth >= 0 ? 'stat-growth-positive' : 'stat-growth-negative';
});

const growthIconClasses = computed(() => {
  if (props.growth === undefined) return '';
  return props.growth >= 0 ? 'text-green-500' : 'text-red-500';
});

const formattedValue = computed(() => {
  if (props.isLoading) return '...';
  
  const value = props.value;
  
  switch (props.format) {
    case 'number':
      return typeof value === 'number' ? value.toLocaleString('ar-SA') : value;
    
    case 'currency':
      if (typeof value === 'number') {
        return adminStore.formatCurrency(value, props.currencyCode);
      }
      return value;
    
    case 'percentage':
      return typeof value === 'number' ? `${value.toFixed(1)}%` : value;
    
    default:
      return value;
  }
});
</script>

<style scoped>
.stat-card {
  @apply relative bg-theme-surface border border-theme-border rounded-xl p-6 overflow-hidden transition-all duration-200 hover:shadow-lg;
}

.stat-card::before {
  content: '';
  @apply absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-theme-primary to-theme-secondary;
}

.stat-card-primary::before {
  @apply from-blue-500 to-blue-600;
}

.stat-card-success::before {
  @apply from-green-500 to-green-600;
}

.stat-card-warning::before {
  @apply from-yellow-500 to-yellow-600;
}

.stat-card-danger::before {
  @apply from-red-500 to-red-600;
}

.stat-card-info::before {
  @apply from-cyan-500 to-cyan-600;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.stat-icon-default {
  @apply bg-theme-surface-light text-theme-primary;
}

.stat-icon-primary {
  @apply bg-blue-100 text-blue-600;
}

.stat-icon-success {
  @apply bg-green-100 text-green-600;
}

.stat-icon-warning {
  @apply bg-yellow-100 text-yellow-600;
}

.stat-icon-danger {
  @apply bg-red-100 text-red-600;
}

.stat-icon-info {
  @apply bg-cyan-100 text-cyan-600;
}

.stat-content {
  @apply space-y-2;
}

.stat-label {
  @apply text-sm font-medium text-theme-secondary;
}

.stat-value {
  @apply text-2xl font-bold text-theme-primary;
}

.stat-growth {
  @apply flex items-center gap-1;
}

.stat-growth-positive {
  @apply text-green-600;
}

.stat-growth-negative {
  @apply text-red-600;
}

.stat-subtitle {
  @apply text-sm text-theme-muted;
}

.stat-loading {
  @apply absolute inset-0 bg-theme-surface bg-opacity-75 flex items-center justify-center;
}

/* Mobile responsive */
@media (max-width: 640px) {
  .stat-card {
    @apply p-4;
  }
  
  .stat-value {
    @apply text-xl;
  }
  
  .stat-icon {
    @apply w-10 h-10 mb-3;
  }
  
  .stat-icon svg {
    @apply w-5 h-5;
  }
}
</style>
