<template>
  <div class="stat-card" :class="cardClasses">
    <!-- Icon -->
    <div class="stat-icon" :class="iconClasses">
      <component :is="icon" class="w-6 h-6" />
    </div>

    <!-- Content -->
    <div class="stat-content">
      <div class="stat-label arabic-text">{{ label }}</div>
      <div class="stat-value arabic-text">{{ formattedValue }}</div>
      
      <!-- Growth indicator -->
      <div v-if="growth !== undefined" class="stat-growth" :class="growthClasses">
        <svg class="w-4 h-4" :class="growthIconClasses" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path v-if="growth >= 0" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
        </svg>
        <span class="text-sm font-medium">
          {{ Math.abs(growth).toFixed(1) }}%
        </span>
        <span class="text-xs text-theme-muted arabic-text">{{ growthLabel }}</span>
      </div>

      <!-- Additional info -->
      <div v-if="subtitle" class="stat-subtitle arabic-text">{{ subtitle }}</div>
    </div>

    <!-- Loading overlay -->
    <div v-if="isLoading" class="stat-loading">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-theme-primary"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  label: string;
  value: string | number;
  icon: any;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info';
  growth?: number;
  growthLabel?: string;
  subtitle?: string;
  isLoading?: boolean;
  format?: 'number' | 'currency' | 'percentage' | 'custom';
  currencyCode?: string;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  growthLabel: 'من الشهر الماضي',
  format: 'custom',
  currencyCode: 'USD'
});

const adminStore = useAdminStore();

const cardClasses = computed(() => {
  const variants = {
    default: 'stat-card-default',
    primary: 'stat-card-primary',
    success: 'stat-card-success',
    warning: 'stat-card-warning',
    danger: 'stat-card-danger',
    info: 'stat-card-info'
  };
  return variants[props.variant];
});

const iconClasses = computed(() => {
  const variants = {
    default: 'stat-icon-default',
    primary: 'stat-icon-primary',
    success: 'stat-icon-success',
    warning: 'stat-icon-warning',
    danger: 'stat-icon-danger',
    info: 'stat-icon-info'
  };
  return variants[props.variant];
});

const growthClasses = computed(() => {
  if (props.growth === undefined) return '';
  return props.growth >= 0 ? 'stat-growth-positive' : 'stat-growth-negative';
});

const growthIconClasses = computed(() => {
  if (props.growth === undefined) return '';
  return props.growth >= 0 ? 'text-green-500' : 'text-red-500';
});

const formattedValue = computed(() => {
  if (props.isLoading) return '...';
  
  const value = props.value;
  
  switch (props.format) {
    case 'number':
      return typeof value === 'number' ? value.toLocaleString('ar-SA') : value;
    
    case 'currency':
      if (typeof value === 'number') {
        return adminStore.formatCurrency(value, props.currencyCode);
      }
      return value;
    
    case 'percentage':
      return typeof value === 'number' ? `${value.toFixed(1)}%` : value;
    
    default:
      return value;
  }
});
</script>

<style scoped>
.stat-card {
  @apply relative overflow-hidden transition-all duration-500 cursor-pointer;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--glass-shadow);
  padding: 2rem;
  animation: slideInUp 0.6s ease-out;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.05), rgba(59, 130, 246, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: var(--border-radius-xl);
}

.stat-card:hover::after {
  opacity: 1;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(var(--color-primary), 0.2);
  border-color: rgba(147, 51, 234, 0.3);
}

.stat-card-primary::before {
  background: linear-gradient(90deg, rgb(59, 130, 246), rgb(37, 99, 235));
}

.stat-card-success::before {
  background: linear-gradient(90deg, rgb(34, 197, 94), rgb(22, 163, 74));
}

.stat-card-warning::before {
  background: linear-gradient(90deg, rgb(245, 158, 11), rgb(217, 119, 6));
}

.stat-card-danger::before {
  background: linear-gradient(90deg, rgb(239, 68, 68), rgb(220, 38, 38));
}

.stat-card-info::before {
  background: linear-gradient(90deg, rgb(6, 182, 212), rgb(8, 145, 178));
}

.stat-icon {
  @apply w-16 h-16 rounded-2xl flex items-center justify-center mb-6 relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: iconFloat 3s ease-in-out infinite;
}

.stat-icon-default {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(59, 130, 246, 0.2));
  color: rgb(var(--color-primary));
  box-shadow: 0 8px 24px rgba(var(--color-primary), 0.2);
}

.stat-icon-primary {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.2));
  color: rgb(59, 130, 246);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.stat-icon-success {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(22, 163, 74, 0.2));
  color: rgb(34, 197, 94);
  box-shadow: 0 8px 24px rgba(34, 197, 94, 0.3);
}

.stat-icon-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.2));
  color: rgb(245, 158, 11);
  box-shadow: 0 8px 24px rgba(245, 158, 11, 0.3);
}

.stat-icon-danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.2));
  color: rgb(239, 68, 68);
  box-shadow: 0 8px 24px rgba(239, 68, 68, 0.3);
}

.stat-icon-info {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.2), rgba(8, 145, 178, 0.2));
  color: rgb(6, 182, 212);
  box-shadow: 0 8px 24px rgba(6, 182, 212, 0.3);
}

.stat-card:hover .stat-icon {
  transform: scale(1.1) rotate(5deg);
  animation-play-state: paused;
}

.stat-content {
  @apply space-y-3 relative z-10;
}

.stat-label {
  @apply text-sm font-semibold;
  color: rgb(var(--color-text-secondary));
  font-family: 'Cairo', sans-serif;
  letter-spacing: 0.5px;
}

.stat-value {
  @apply text-3xl font-bold;
  color: rgb(var(--color-text-primary));
  font-family: 'Cairo', sans-serif;
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: valueGlow 2s ease-in-out infinite alternate;
}

.stat-growth {
  @apply flex items-center gap-2 p-2 rounded-lg;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-family: 'Cairo', sans-serif;
}

.stat-growth-positive {
  color: rgb(34, 197, 94);
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.2);
}

.stat-growth-negative {
  color: rgb(239, 68, 68);
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.stat-subtitle {
  @apply text-sm;
  color: rgb(var(--color-text-muted));
  font-family: 'Cairo', sans-serif;
}

.stat-loading {
  @apply absolute inset-0 flex items-center justify-center;
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-xl);
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

@keyframes valueGlow {
  from {
    filter: brightness(1);
  }
  to {
    filter: brightness(1.2);
  }
}

/* Mobile responsive */
@media (max-width: 640px) {
  .stat-card {
    padding: 1.5rem;
  }

  .stat-value {
    @apply text-2xl;
  }

  .stat-icon {
    @apply w-12 h-12 mb-4;
  }

  .stat-icon svg {
    @apply w-6 h-6;
  }

  .stat-content {
    @apply space-y-2;
  }

  .stat-growth {
    @apply text-sm;
  }
}

/* Tablet responsive */
@media (max-width: 1024px) and (min-width: 641px) {
  .stat-card {
    padding: 1.75rem;
  }

  .stat-icon {
    @apply w-14 h-14 mb-5;
  }
}

/* Loading shimmer effect */
.stat-loading .animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* RTL Support */
[dir="rtl"] .stat-growth {
  flex-direction: row-reverse;
}

/* Accessibility */
.stat-card:focus {
  outline: 2px solid rgb(var(--color-primary));
  outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .stat-card {
    border-width: 2px;
  }

  .stat-value {
    -webkit-text-fill-color: rgb(var(--color-text-primary));
    background: none;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .stat-card,
  .stat-icon,
  .stat-value {
    animation: none;
    transition: none;
  }

  .stat-card:hover {
    transform: none;
  }
}
</style>
