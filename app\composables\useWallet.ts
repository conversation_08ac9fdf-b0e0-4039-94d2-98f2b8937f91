import { ref, computed } from 'vue'

export interface WalletTransaction {
  id: string
  type: 'deposit' | 'purchase'
  title: string
  amount: number
  currency: 'SAR'
  status: 'completed' | 'pending' | 'failed'
  created_at: string
  description?: string
}

export interface WalletBalance {
  sar: number
  usd: number
  total_sar: number
}

export interface WalletStats {
  total_balance: number
  total_deposits: number
  total_expenses: number
  transaction_count: number
}

export const useWallet = () => {
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const currentCurrency = ref<'SAR' | 'USD'>('SAR')
  const transactionFilter = ref<'all' | 'deposits' | 'expenses'>('all')
  const currentPage = ref(1)
  const itemsPerPage = 10

  // Exchange rate (mock - should come from useExchangeRates)
  const exchangeRate = ref(3.75) // 1 USD = 3.75 SAR

  // Mock data for development
  const mockTransactions: WalletTransaction[] = [
    {
      id: '1',
      type: 'deposit',
      title: 'إيداع بنكي',
      amount: 500,
      currency: 'SAR',
      status: 'completed',
      created_at: '2024-01-15T10:30:00Z',
      description: 'تحويل من البنك الأهلي'
    },
    {
      id: '2',
      type: 'purchase',
      title: 'شراء بطاقة ستيم',
      amount: 50,
      currency: 'SAR',
      status: 'completed',
      created_at: '2024-01-14T15:45:00Z',
      description: 'بطاقة هدايا ستيم 50 ريال'
    },
    {
      id: '3',
      type: 'deposit',
      title: 'إيداع STC Pay',
      amount: 200,
      currency: 'SAR',
      status: 'pending',
      created_at: '2024-01-13T09:15:00Z',
      description: 'إيداع عبر STC Pay'
    },
    {
      id: '4',
      type: 'purchase',
      title: 'شراء بطاقة جوجل بلاي',
      amount: 100,
      currency: 'SAR',
      status: 'completed',
      created_at: '2024-01-12T14:20:00Z',
      description: 'بطاقة جوجل بلاي 100 ريال'
    },
    {
      id: '5',
      type: 'deposit',
      title: 'إيداع مدى',
      amount: 300,
      currency: 'SAR',
      status: 'completed',
      created_at: '2024-01-11T11:00:00Z',
      description: 'إيداع عبر بطاقة مدى'
    },
    {
      id: '6',
      type: 'purchase',
      title: 'شراء بطاقة أمازون',
      amount: 75,
      currency: 'SAR',
      status: 'failed',
      created_at: '2024-01-10T16:30:00Z',
      description: 'بطاقة أمازون 75 ريال'
    }
  ]

  const mockBalance: WalletBalance = {
    sar: 1250,
    usd: 333.33,
    total_sar: 1250
  }

  // Reactive state
  const transactions = ref<WalletTransaction[]>(mockTransactions)
  const balance = ref<WalletBalance>(mockBalance)

  // Computed properties
  const stats = computed((): WalletStats => {
    const deposits = transactions.value
      .filter(t => t.type === 'deposit' && t.status === 'completed')
      .reduce((sum, t) => sum + t.amount, 0)
    
    const expenses = transactions.value
      .filter(t => t.type === 'purchase' && t.status === 'completed')
      .reduce((sum, t) => sum + t.amount, 0)

    return {
      total_balance: balance.value.sar,
      total_deposits: deposits,
      total_expenses: expenses,
      transaction_count: transactions.value.length
    }
  })

  const filteredTransactions = computed(() => {
    let filtered = transactions.value

    if (transactionFilter.value === 'deposits') {
      filtered = filtered.filter(t => t.type === 'deposit')
    } else if (transactionFilter.value === 'expenses') {
      filtered = filtered.filter(t => t.type === 'purchase')
    }

    return filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
  })

  const paginatedTransactions = computed(() => {
    const start = (currentPage.value - 1) * itemsPerPage
    const end = start + itemsPerPage
    return filteredTransactions.value.slice(start, end)
  })

  const totalPages = computed(() => {
    return Math.ceil(filteredTransactions.value.length / itemsPerPage)
  })

  const formattedBalance = computed(() => {
    if (currentCurrency.value === 'USD') {
      return {
        amount: (balance.value.sar / exchangeRate.value).toLocaleString('en-US', { minimumFractionDigits: 2 }),
        currency: 'USD',
        symbol: '$'
      }
    }
    return {
      amount: balance.value.sar.toLocaleString('ar-SA'),
      currency: 'SAR',
      symbol: 'ر.س'
    }
  })

  // Methods
  const fetchWalletData = async () => {
    isLoading.value = true
    error.value = null

    try {
      // TODO: Replace with actual API calls
      // const [balanceData, transactionsData] = await Promise.all([
      //   $fetch('/backend/api/wallet/balance'),
      //   $fetch('/backend/api/wallet/transactions')
      // ])

      // For now, use mock data
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay
      
      balance.value = mockBalance
      transactions.value = mockTransactions
    } catch (err) {
      error.value = 'فشل في تحميل بيانات المحفظة'
      console.error('Wallet fetch error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const refreshWallet = async () => {
    await fetchWalletData()
  }

  const toggleCurrency = () => {
    currentCurrency.value = currentCurrency.value === 'SAR' ? 'USD' : 'SAR'
  }

  const setTransactionFilter = (filter: 'all' | 'deposits' | 'expenses') => {
    transactionFilter.value = filter
    currentPage.value = 1 // Reset to first page when filtering
  }

  const setPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
    }
  }

  const exportTransactions = () => {
    const headers = ['التاريخ', 'النوع', 'العنوان', 'المبلغ', 'العملة', 'الحالة', 'الوصف']
    const csvContent = [
      headers.join(','),
      ...filteredTransactions.value.map(t => [
        new Date(t.created_at).toLocaleDateString('ar-SA'),
        t.type === 'deposit' ? 'إيداع' : 'شراء',
        `"${t.title}"`,
        t.amount,
        t.currency,
        t.status === 'completed' ? 'مكتمل' : t.status === 'pending' ? 'قيد التنفيذ' : 'مرفوض',
        `"${t.description || ''}"`
      ].join(','))
    ].join('\n')

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `wallet-transactions-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const formatTransactionDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTransactionIcon = (type: string) => {
    return type === 'deposit' ? '💳' : '🛒'
  }

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'failed':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getAmountClass = (type: string) => {
    return type === 'deposit' ? 'text-green-400' : 'text-red-400'
  }

  return {
    // State
    transactions: readonly(transactions),
    balance: readonly(balance),
    isLoading: readonly(isLoading),
    error: readonly(error),
    currentCurrency: readonly(currentCurrency),
    transactionFilter: readonly(transactionFilter),
    currentPage: readonly(currentPage),
    
    // Computed
    stats,
    filteredTransactions,
    paginatedTransactions,
    totalPages,
    formattedBalance,
    
    // Methods
    fetchWalletData,
    refreshWallet,
    toggleCurrency,
    setTransactionFilter,
    setPage,
    exportTransactions,
    formatTransactionDate,
    getTransactionIcon,
    getStatusBadgeClass,
    getAmountClass
  }
}
