import { defineStore } from 'pinia';

// ===== INTERFACES =====
export interface AdminUser {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'worker' | 'distributor' | 'user';
  walletBalance: number; // Always in USD
  status: 'active' | 'suspended' | 'pending';
  createdAt: string;
  lastLogin?: string;
  phone?: string;
  avatar?: string;
  // Worker-specific fields
  assignedOrders?: number;
  successRate?: number;
  performance?: 'excellent' | 'good' | 'average' | 'poor';
}

export interface AdminProduct {
  id: number;
  name: string;
  nameAr: string;
  type: 'simple' | 'package' | 'code';
  basePriceUSD: number; // Always stored in USD
  status: 'active' | 'inactive' | 'archived';
  createdAt: string;
  updatedAt: string;
  description?: string;
  descriptionAr?: string;
  image?: string;
  category?: string;
  categoryAr?: string;
  // Currency-specific pricing overrides
  currencyPrices?: Record<string, number>;
  // Digital codes for 'code' type products
  codes?: string[];
  codesUsed?: number;
  codesTotal?: number;
}

export interface AdminOrder {
  id: number;
  productId: number;
  productName: string;
  productNameAr: string;
  userId: number;
  userName: string;
  userEmail: string;
  amountUSD: number; // Always stored in USD
  displayCurrency: string;
  displayAmount: number;
  status: 'pending' | 'processing' | 'completed' | 'cancelled' | 'refunded';
  createdAt: string;
  updatedAt: string;
  workerId?: number;
  workerName?: string;
  notes?: string;
  // Profit calculation (in USD)
  costUSD?: number;
  profitUSD?: number;
}

export interface WalletTransaction {
  id: number;
  userId: number;
  userName: string;
  userEmail: string;
  type: 'deposit' | 'withdrawal' | 'refund' | 'bonus';
  amountUSD: number; // Always stored in USD
  displayCurrency: string;
  displayAmount: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  method?: string;
  reference?: string;
  notes?: string;
}

export interface Currency {
  id: number;
  code: string; // e.g., 'SAR', 'EUR'
  symbol: string; // e.g., 'ر.س', '€'
  nameAr: string;
  nameEn: string;
  exchangeRate: number; // Rate relative to USD (1 USD = X currency)
  status: 'active' | 'inactive';
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AdminStats {
  totalUsers: number;
  totalOrders: number;
  totalRevenueUSD: number;
  pendingOrders: number;
  totalDepositsUSD: number;
  totalWithdrawalsUSD: number;
  currentBalanceUSD: number;
  // Growth percentages
  usersGrowth: number;
  ordersGrowth: number;
  revenueGrowth: number;
}

// ===== ADMIN STORE =====
export const useAdminStore = defineStore('admin', {
  state: () => ({
    // Loading states
    isLoading: false,
    isLoadingUsers: false,
    isLoadingProducts: false,
    isLoadingOrders: false,
    isLoadingWallet: false,
    isLoadingCurrencies: false,
    
    // Data
    users: [] as AdminUser[],
    products: [] as AdminProduct[],
    orders: [] as AdminOrder[],
    walletTransactions: [] as WalletTransaction[],
    currencies: [] as Currency[],
    stats: null as AdminStats | null,
    
    // Pagination & Filters
    currentPage: 1,
    itemsPerPage: 20,
    totalItems: 0,
    
    // Filters
    userFilters: {
      role: '',
      status: '',
      search: ''
    },
    productFilters: {
      type: '',
      status: '',
      category: '',
      search: ''
    },
    orderFilters: {
      status: '',
      dateFrom: '',
      dateTo: '',
      search: ''
    },
    walletFilters: {
      type: '',
      status: '',
      currency: '',
      dateFrom: '',
      dateTo: '',
      search: ''
    }
  }),

  getters: {
    // User getters
    activeUsers: (state) => state.users.filter(u => u.status === 'active'),
    adminUsers: (state) => state.users.filter(u => u.role === 'admin'),
    workerUsers: (state) => state.users.filter(u => u.role === 'worker'),
    regularUsers: (state) => state.users.filter(u => u.role === 'user'),
    
    // Product getters
    activeProducts: (state) => state.products.filter(p => p.status === 'active'),
    productsByType: (state) => (type: string) => state.products.filter(p => p.type === type),
    
    // Order getters
    pendingOrders: (state) => state.orders.filter(o => o.status === 'pending'),
    completedOrders: (state) => state.orders.filter(o => o.status === 'completed'),
    
    // Currency getters
    activeCurrencies: (state) => state.currencies.filter(c => c.status === 'active'),
    defaultCurrency: (state) => state.currencies.find(c => c.isDefault) || {
      code: 'USD',
      symbol: '$',
      nameAr: 'دولار أمريكي',
      nameEn: 'US Dollar',
      exchangeRate: 1,
      isDefault: true
    },
    
    // Stats getters
    totalRevenueInCurrency: (state) => (currencyCode: string) => {
      if (!state.stats) return 0;
      const currency = state.currencies.find(c => c.code === currencyCode);
      if (!currency) return state.stats.totalRevenueUSD;
      return state.stats.totalRevenueUSD * currency.exchangeRate;
    }
  },

  actions: {
    // ===== INITIALIZATION =====
    async initializeAdmin() {
      this.isLoading = true;
      try {
        await Promise.all([
          this.fetchStats(),
          this.fetchCurrencies()
        ]);
      } catch (error) {
        console.error('Failed to initialize admin:', error);
      } finally {
        this.isLoading = false;
      }
    },

    // ===== STATS =====
    async fetchStats() {
      // Mock implementation - replace with real API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      this.stats = {
        totalUsers: 1247,
        totalOrders: 3891,
        totalRevenueUSD: 125430.50,
        pendingOrders: 23,
        totalDepositsUSD: 89234.20,
        totalWithdrawalsUSD: 45123.80,
        currentBalanceUSD: 44110.40,
        usersGrowth: 12.5,
        ordersGrowth: 8.3,
        revenueGrowth: 15.7
      };
    },

    // ===== CURRENCIES =====
    async fetchCurrencies() {
      this.isLoadingCurrencies = true;
      try {
        // Mock implementation - replace with real API call
        await new Promise(resolve => setTimeout(resolve, 300));
        
        this.currencies = [
          {
            id: 1,
            code: 'USD',
            symbol: '$',
            nameAr: 'دولار أمريكي',
            nameEn: 'US Dollar',
            exchangeRate: 1,
            status: 'active',
            isDefault: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          },
          {
            id: 2,
            code: 'SAR',
            symbol: 'ر.س',
            nameAr: 'ريال سعودي',
            nameEn: 'Saudi Riyal',
            exchangeRate: 3.75,
            status: 'active',
            isDefault: false,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          },
          {
            id: 3,
            code: 'EUR',
            symbol: '€',
            nameAr: 'يورو',
            nameEn: 'Euro',
            exchangeRate: 0.85,
            status: 'active',
            isDefault: false,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z'
          }
        ];
      } catch (error) {
        console.error('Failed to fetch currencies:', error);
      } finally {
        this.isLoadingCurrencies = false;
      }
    },

    // ===== UTILITY METHODS =====
    convertFromUSD(amountUSD: number, currencyCode: string): number {
      const currency = this.currencies.find(c => c.code === currencyCode);
      if (!currency) return amountUSD;
      return amountUSD * currency.exchangeRate;
    },

    convertToUSD(amount: number, currencyCode: string): number {
      const currency = this.currencies.find(c => c.code === currencyCode);
      if (!currency) return amount;
      return amount / currency.exchangeRate;
    },

    formatCurrency(amount: number, currencyCode: string): string {
      const currency = this.currencies.find(c => c.code === currencyCode);
      if (!currency) return `$${amount.toFixed(2)}`;
      
      return `${currency.symbol}${amount.toFixed(2)}`;
    }
  }
});
