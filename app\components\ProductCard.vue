<template>
  <NuxtLink :to="`/product/${product.slug}`">
    <div class="product-card h-full flex flex-col group">
      <!-- Product Image - Square aspect ratio (1:1) - 114.67 x 114.67 -->
      <div class="relative aspect-square overflow-hidden">
        <img
          :src="product.coverImage || '/logo.jpg'"
          :alt="product.title"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />

        <!-- Discount Badge -->
        <div
          v-if="cheapestPackage?.discount"
          class="absolute top-1 right-1 badge badge-discount"
        >
          -{{ cheapestPackage.discount }}%
        </div>

        <!-- Featured Badge -->
        <div
          v-if="product.featured"
          class="absolute top-1 right-1 badge badge-featured"
        >
          مميز
        </div>
      </div>

      <!-- Product Info - 114.67 x 71 -->
      <div class="p-2 flex flex-col justify-between bg-theme-surface" style="height: 71px;">
        <!-- Product Title -->
        <h3 class="text-xs font-semibold product-title mb-1 line-clamp-2 leading-tight">
          {{ product.title }}
        </h3>

        <!-- Bottom Section: Rating + Price -->
        <div class="flex items-center justify-between">
          <!-- Rating -->
          <div class="flex items-center space-x-1 space-x-reverse">
            <span class="badge-rating font-bold text-xs">{{ product.rating }}</span>
            <div class="flex">
              <svg
                v-for="i in 5"
                :key="i"
                class="w-2 h-2 badge-rating fill-current"
                viewBox="0 0 24 24"
              >
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
          </div>

          <!-- Price Section -->
          <div class="text-left">
            <div class="product-price text-xs">
              ${{ cheapestPackage?.price.toFixed(2) }}
            </div>
            <div
              v-if="cheapestPackage?.originalPrice"
              class="text-theme-muted text-xs line-through -mt-0.5"
            >
              ${{ cheapestPackage.originalPrice.toFixed(2) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </NuxtLink>
</template>

<script setup lang="ts">
import type { Product } from '~/data/mockData'

interface Props {
  product: Product
}

const props = defineProps<Props>()

// Get the cheapest package for display
const cheapestPackage = computed(() => {
  if (!props.product.packages || props.product.packages.length === 0) return null
  return props.product.packages.reduce(
    (min, pkg) => (pkg.price < min.price ? pkg : min),
    props.product.packages[0]
  )
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
