// TODO: backend - Replace with real database integration
export default defineEventHandler(async (event) => {
  // Mock orders data
  const orders = [
    {
      id: 1,
      userId: 4,
      productId: 1,
      productName: "Premium Gaming Laptop",
      packageName: "Pro",
      quantity: 1,
      totalPrice: 1599.99,
      currency: "usd",
      status: "completed",
      profit: 299.99,
      declineNote: null,
      digitalCode: "LAPTOP-PRO-ABC123",
      customFields: {
        "Color": "Black",
        "Keyboard Layout": "US"
      },
      createdAt: "2025-07-25T14:30:00Z",
      completedAt: "2025-07-25T15:45:00Z"
    },
    {
      id: 2,
      userId: 4,
      productId: 2,
      productName: "Wireless Headphones",
      packageName: "Standard",
      quantity: 2,
      totalPrice: 599.98,
      currency: "usd",
      status: "pending",
      profit: 0,
      declineNote: null,
      digitalCode: null,
      customFields: {
        "Color": "Black"
      },
      createdAt: "2025-07-28T10:15:00Z",
      completedAt: null
    },
    {
      id: 3,
      userId: 3,
      productId: 3,
      productName: "Smart Watch",
      packageName: "Premium",
      quantity: 1,
      totalPrice: 499.99,
      currency: "eur",
      status: "declined",
      profit: 0,
      declineNote: "Out of stock for selected color",
      digitalCode: null,
      customFields: {
        "Band Color": "Red",
        "Size": "44mm"
      },
      createdAt: "2025-07-26T16:20:00Z",
      completedAt: null
    },
    {
      id: 4,
      userId: 4,
      productId: 5,
      productName: "Running Shoes",
      packageName: "Pro",
      quantity: 1,
      totalPrice: 199.99,
      currency: "usd",
      status: "completed",
      profit: 49.99,
      declineNote: null,
      digitalCode: "SHOES-PRO-XYZ789",
      customFields: {
        "Size": "10",
        "Color": "Blue"
      },
      createdAt: "2025-07-20T09:45:00Z",
      completedAt: "2025-07-21T11:30:00Z"
    },
    {
      id: 5,
      userId: 2,
      productId: 4,
      productName: "Coffee Maker",
      packageName: "Professional",
      quantity: 1,
      totalPrice: 799.99,
      currency: "eur",
      status: "pending",
      profit: 0,
      declineNote: null,
      digitalCode: null,
      customFields: {
        "Color": "Stainless Steel"
      },
      createdAt: "2025-07-29T08:00:00Z",
      completedAt: null
    }
  ];

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 120));

  return {
    success: true,
    data: orders,
    total: orders.length
  };
});
