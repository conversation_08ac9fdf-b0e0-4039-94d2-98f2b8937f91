<template>
  <div>
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Products Management</h1>
        <p class="text-lg text-gray-600">Manage your product catalog</p>
      </div>
      <button 
        @click="showCreateModal = true"
        class="btn-primary"
      >
        Add New Product
      </button>
    </div>

    <!-- Filters -->
    <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <input 
            v-model="searchQuery"
            type="text" 
            placeholder="Search products..."
            class="input-field"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
          <select v-model="categoryFilter" class="input-field">
            <option value="">All Categories</option>
            <option value="electronics">Electronics</option>
            <option value="home">Home & Garden</option>
            <option value="sports">Sports</option>
            <option value="books">Books</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Stock Status</label>
          <select v-model="stockFilter" class="input-field">
            <option value="">All Products</option>
            <option value="in-stock">In Stock</option>
            <option value="low-stock">Low Stock (≤10)</option>
            <option value="out-of-stock">Out of Stock</option>
          </select>
        </div>

        <div class="flex items-end">
          <button 
            @click="clearFilters"
            class="btn-secondary text-sm w-full"
          >
            Clear Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Products Table -->
    <div class="card overflow-hidden">
      <div v-if="pending" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading products...</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Product
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stock
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="product in filteredProducts" :key="product.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <img 
                    :src="product.image || '/images/placeholder.jpg'" 
                    :alt="product.name"
                    class="w-10 h-10 object-cover rounded-lg bg-gray-200"
                  />
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                    <div class="text-sm text-gray-500">ID: {{ product.id }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="badge bg-gray-100 text-gray-800 capitalize">
                  {{ product.category }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${{ product.price.toFixed(2) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  :class="getStockClass(product.stock)"
                  class="text-sm font-medium"
                >
                  {{ product.stock }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  :class="product.stock > 0 ? 'status-completed' : 'bg-red-100 text-red-800'"
                  class="badge"
                >
                  {{ product.stock > 0 ? 'Active' : 'Out of Stock' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button 
                  @click="editProduct(product)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  Edit
                </button>
                <button 
                  @click="deleteProduct(product.id)"
                  class="text-red-600 hover:text-red-900"
                >
                  Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-if="filteredProducts.length === 0" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No products found</h3>
          <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
        </div>
      </div>
    </div>

    <!-- Create/Edit Product Modal -->
    <ProductModal 
      v-if="showCreateModal || showEditModal"
      :product="editingProduct"
      :is-editing="showEditModal"
      @close="closeModals"
      @success="handleProductSuccess"
    />
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'dashboard',
  middleware: 'admin'
});

// SEO Meta
useHead({
  title: 'Products Management - Admin Dashboard',
  meta: [
    { name: 'description', content: 'Manage your product catalog, add new products, and update inventory.' }
  ]
});

// Data fetching
const { data: products, pending, refresh } = await useAsyncData('admin-products', () => 
  $fetch('/backend/api/products')
);

// Filters
const searchQuery = ref('');
const categoryFilter = ref('');
const stockFilter = ref('');

// Modals
const showCreateModal = ref(false);
const showEditModal = ref(false);
const editingProduct = ref(null);

// Computed properties
const filteredProducts = computed(() => {
  let filtered = products.value?.data || [];

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter((product: any) => 
      product.name.toLowerCase().includes(query) ||
      product.description.toLowerCase().includes(query)
    );
  }

  // Category filter
  if (categoryFilter.value) {
    filtered = filtered.filter((product: any) => 
      product.category === categoryFilter.value
    );
  }

  // Stock filter
  if (stockFilter.value) {
    switch (stockFilter.value) {
      case 'in-stock':
        filtered = filtered.filter((product: any) => product.stock > 10);
        break;
      case 'low-stock':
        filtered = filtered.filter((product: any) => product.stock > 0 && product.stock <= 10);
        break;
      case 'out-of-stock':
        filtered = filtered.filter((product: any) => product.stock === 0);
        break;
    }
  }

  return filtered;
});

// Methods
const getStockClass = (stock: number) => {
  if (stock === 0) return 'text-red-600';
  if (stock <= 10) return 'text-yellow-600';
  return 'text-green-600';
};

const clearFilters = () => {
  searchQuery.value = '';
  categoryFilter.value = '';
  stockFilter.value = '';
};

const editProduct = (product: any) => {
  editingProduct.value = product;
  showEditModal.value = true;
};

const deleteProduct = async (productId: number) => {
  if (confirm('Are you sure you want to delete this product?')) {
    // TODO: backend - Implement product deletion API
    console.log('Deleting product:', productId);
    await refresh();
  }
};

const closeModals = () => {
  showCreateModal.value = false;
  showEditModal.value = false;
  editingProduct.value = null;
};

const handleProductSuccess = async () => {
  closeModals();
  await refresh();
};
</script>
