<template>
  <div>
    <!-- <PERSON>er -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">Orders Management</h1>
      <p class="text-lg text-gray-600">View and manage customer orders</p>
    </div>

    <!-- Order Filters -->
    <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <input 
            v-model="searchQuery"
            type="text" 
            placeholder="Search orders..."
            class="input-field"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select v-model="statusFilter" class="input-field">
            <option value="">All Orders</option>
            <option value="pending">Pending</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
          <select v-model="dateFilter" class="input-field">
            <option value="">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>

        <div class="flex items-end">
          <button 
            @click="clearFilters"
            class="btn-secondary text-sm w-full"
          >
            Clear Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Orders Table -->
    <div class="card overflow-hidden">
      <div v-if="pending" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading orders...</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Order
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Items
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Total
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="order in filteredOrders" :key="order.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">#{{ order.id }}</div>
                <div class="text-sm text-gray-500">{{ order.paymentMethod }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ order.customerName }}</div>
                <div class="text-sm text-gray-500">{{ order.customerEmail }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(order.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ order.items.length }} item{{ order.items.length !== 1 ? 's' : '' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                ${{ order.total.toFixed(2) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <select 
                  :value="order.status"
                  @change="updateOrderStatus(order.id, $event.target.value)"
                  :class="getStatusClass(order.status)"
                  class="text-xs font-medium px-2 py-1 rounded-full border-0 focus:ring-2 focus:ring-blue-500"
                >
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button 
                  @click="viewOrder(order)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  View
                </button>
                <button 
                  v-if="order.status === 'pending'"
                  @click="cancelOrder(order.id)"
                  class="text-red-600 hover:text-red-900"
                >
                  Cancel
                </button>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-if="filteredOrders.length === 0" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No orders found</h3>
          <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
        </div>
      </div>
    </div>

    <!-- Order Details Modal -->
    <OrderDetailsModal 
      v-if="showOrderModal"
      :order="selectedOrder"
      @close="showOrderModal = false"
      @status-updated="handleStatusUpdate"
    />
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'dashboard',
  middleware: ['auth', 'worker'] // Workers can also manage orders
});

// SEO Meta
useHead({
  title: 'Orders Management - Admin Dashboard',
  meta: [
    { name: 'description', content: 'Manage customer orders, update statuses, and track fulfillment.' }
  ]
});

// Data fetching
const { data: orders, pending, refresh } = await useAsyncData('admin-orders', () => 
  $fetch('/backend/api/orders')
);

// Filters
const searchQuery = ref('');
const statusFilter = ref('');
const dateFilter = ref('');

// Modal
const showOrderModal = ref(false);
const selectedOrder = ref(null);

// Computed properties
const filteredOrders = computed(() => {
  let filtered = orders.value?.data || [];

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter((order: any) => 
      order.id.toString().includes(query) ||
      order.customerName.toLowerCase().includes(query) ||
      order.customerEmail.toLowerCase().includes(query)
    );
  }

  // Status filter
  if (statusFilter.value) {
    filtered = filtered.filter((order: any) => order.status === statusFilter.value);
  }

  // Date filter
  if (dateFilter.value) {
    const now = new Date();
    const filterDate = new Date();
    
    switch (dateFilter.value) {
      case 'today':
        filterDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        filterDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        filterDate.setMonth(now.getMonth() - 1);
        break;
    }
    
    filtered = filtered.filter((order: any) => 
      new Date(order.createdAt) >= filterDate
    );
  }

  // Sort by date (newest first)
  return filtered.sort((a: any, b: any) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
});

// Methods
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getStatusClass = (status: string) => {
  const statusClasses = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800'
  };
  return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800';
};

const clearFilters = () => {
  searchQuery.value = '';
  statusFilter.value = '';
  dateFilter.value = '';
};

const viewOrder = (order: any) => {
  selectedOrder.value = order;
  showOrderModal.value = true;
};

const updateOrderStatus = async (orderId: number, newStatus: string) => {
  // TODO: backend - Implement order status update API
  console.log('Updating order status:', orderId, newStatus);
  await refresh();
};

const cancelOrder = async (orderId: number) => {
  if (confirm('Are you sure you want to cancel this order?')) {
    await updateOrderStatus(orderId, 'cancelled');
  }
};

const handleStatusUpdate = async () => {
  showOrderModal.value = false;
  await refresh();
};
</script>
