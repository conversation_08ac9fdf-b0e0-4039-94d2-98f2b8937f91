// TODO: backend - Replace with real database integration
export default defineEventHandler(async (event) => {
  // Mock products data
  const products = [
    {
      id: 1,
      name: "Premium Gaming Laptop",
      description: "High-performance gaming laptop with RTX 4080",
      price: 1299.99,
      category: "electronics",
      image: "/images/laptop.jpg",
      stock: 15,
      packages: [
        { id: 1, name: "Basic", price: 1299.99, features: ["16GB RAM", "512GB SSD"] },
        { id: 2, name: "Pro", price: 1599.99, features: ["32GB RAM", "1TB SSD", "Extended Warranty"] }
      ],
      customFields: [
        { name: "Color", type: "select", options: ["Black", "Silver", "White"] },
        { name: "Keyboard Layout", type: "select", options: ["US", "UK", "DE"] }
      ],
      digitalCodes: ["GAME123", "SOFT456", "TRIAL789"]
    },
    {
      id: 2,
      name: "Wireless Headphones",
      description: "Premium noise-cancelling wireless headphones",
      price: 299.99,
      category: "electronics",
      image: "/images/headphones.jpg",
      stock: 25,
      packages: [
        { id: 3, name: "Standard", price: 299.99, features: ["Noise Cancelling", "30h Battery"] },
        { id: 4, name: "Deluxe", price: 399.99, features: ["Noise Cancelling", "40h Battery", "Premium Case"] }
      ],
      customFields: [
        { name: "Color", type: "select", options: ["Black", "White", "Blue"] }
      ],
      digitalCodes: ["MUSIC001", "AUDIO002"]
    },
    {
      id: 3,
      name: "Smart Watch",
      description: "Advanced fitness tracking smartwatch",
      price: 399.99,
      category: "electronics",
      image: "/images/smartwatch.jpg",
      stock: 30,
      packages: [
        { id: 5, name: "Sport", price: 399.99, features: ["GPS", "Heart Rate Monitor"] },
        { id: 6, name: "Premium", price: 499.99, features: ["GPS", "Heart Rate Monitor", "Cellular", "Premium Band"] }
      ],
      customFields: [
        { name: "Band Color", type: "select", options: ["Black", "White", "Red", "Blue"] },
        { name: "Size", type: "select", options: ["40mm", "44mm"] }
      ],
      digitalCodes: ["HEALTH123", "FITNESS456"]
    },
    {
      id: 4,
      name: "Coffee Maker",
      description: "Professional espresso coffee maker",
      price: 599.99,
      category: "home",
      image: "/images/coffee.jpg",
      stock: 12,
      packages: [
        { id: 7, name: "Basic", price: 599.99, features: ["15 Bar Pressure", "Milk Frother"] },
        { id: 8, name: "Professional", price: 799.99, features: ["15 Bar Pressure", "Dual Boiler", "Premium Grinder"] }
      ],
      customFields: [
        { name: "Color", type: "select", options: ["Stainless Steel", "Black", "Red"] }
      ],
      digitalCodes: ["COFFEE001", "RECIPE002"]
    },
    {
      id: 5,
      name: "Running Shoes",
      description: "Professional running shoes with advanced cushioning",
      price: 149.99,
      category: "sports",
      image: "/images/shoes.jpg",
      stock: 50,
      packages: [
        { id: 9, name: "Standard", price: 149.99, features: ["Breathable Mesh", "Cushioned Sole"] },
        { id: 10, name: "Pro", price: 199.99, features: ["Breathable Mesh", "Advanced Cushioning", "Carbon Plate"] }
      ],
      customFields: [
        { name: "Size", type: "select", options: ["7", "8", "9", "10", "11", "12"] },
        { name: "Color", type: "select", options: ["Black", "White", "Blue", "Red"] }
      ],
      digitalCodes: ["TRAINING001", "WORKOUT002"]
    }
  ];

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));

  return {
    success: true,
    data: products,
    total: products.length
  };
});
