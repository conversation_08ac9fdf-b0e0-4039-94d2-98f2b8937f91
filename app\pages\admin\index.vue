<template>
  <div class="space-y-8" dir="rtl">
    <!-- Page Header -->
    <div class="mb-8">
      <h1 class="text-4xl font-bold mb-4" style="color: rgb(var(--color-text-primary)); font-family: 'Cairo', sans-serif;">
        لوحة المعلومات الرئيسية
      </h1>
      <p class="text-lg" style="color: rgb(var(--color-text-secondary)); font-family: 'Cairo', sans-serif;">
        نظرة عامة على منصة التجارة الإلكترونية الخاصة بك
      </p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Users -->
      <StatCard
        :value="stats.totalUsers"
        label="إجمالي المستخدمين"
        icon="users"
        variant="primary"
        :growth="12.5"
        subtitle="زيادة هذا الشهر"
      />

      <!-- Total Products -->
      <StatCard
        :value="stats.totalProducts"
        label="إجمالي المنتجات"
        icon="package"
        variant="success"
        :growth="8.2"
        subtitle="منتجات جديدة"
      />

      <!-- Total Orders -->
      <StatCard
        :value="stats.totalOrders"
        label="إجمالي الطلبات"
        icon="shopping-cart"
        variant="warning"
        :growth="15.3"
        subtitle="طلبات هذا الأسبوع"
      />

      <!-- Revenue -->
      <StatCard
        :value="formatCurrency(stats.totalRevenue)"
        label="إجمالي الإيرادات"
        icon="dollar-sign"
        variant="info"
        :growth="22.1"
        subtitle="نمو الإيرادات"
      />
    </div>

    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Recent Orders -->
      <div class="admin-card">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-bold" style="color: rgb(var(--color-text-primary)); font-family: 'Cairo', sans-serif;">
            الطلبات الأخيرة
          </h2>
          <NuxtLink
            to="/admin/orders"
            class="text-sm font-semibold transition-all duration-300 hover:scale-105"
            style="color: rgb(var(--color-primary)); font-family: 'Cairo', sans-serif;"
          >
            عرض الكل
          </NuxtLink>
        </div>

        <div v-if="recentOrders.length > 0" class="space-y-4">
          <div
            v-for="order in recentOrders"
            :key="order.id"
            class="order-item"
          >
            <div>
              <p class="font-semibold" style="color: rgb(var(--color-text-primary)); font-family: 'Cairo', sans-serif;">
                طلب #{{ order.id }}
              </p>
              <p class="text-sm" style="color: rgb(var(--color-text-secondary)); font-family: 'Cairo', sans-serif;">
                {{ order.customerName }}
              </p>
            </div>
            <div class="text-left">
              <p class="font-bold" style="color: rgb(var(--color-text-primary)); font-family: 'Cairo', sans-serif;">
                {{ formatCurrency(order.total) }}
              </p>
              <span :class="getStatusClass(order.status)" class="status-badge">
                {{ getStatusLabel(order.status) }}
              </span>
            </div>
          </div>
        </div>

        <div v-else class="empty-state">
          <p style="font-family: 'Cairo', sans-serif;">لا توجد طلبات حديثة</p>
        </div>
      </div>

      <!-- Low Stock Products -->
      <div class="admin-card">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-bold" style="color: rgb(var(--color-text-primary)); font-family: 'Cairo', sans-serif;">
            تنبيه المخزون المنخفض
          </h2>
          <NuxtLink
            to="/admin/products"
            class="text-sm font-semibold transition-all duration-300 hover:scale-105"
            style="color: rgb(var(--color-primary)); font-family: 'Cairo', sans-serif;"
          >
            إدارة المنتجات
          </NuxtLink>
        </div>

        <div v-if="lowStockProducts.length > 0" class="space-y-4">
          <div
            v-for="product in lowStockProducts"
            :key="product.id"
            class="product-item"
          >
            <div>
              <p class="font-semibold" style="color: rgb(var(--color-text-primary)); font-family: 'Cairo', sans-serif;">
                {{ product.name }}
              </p>
              <p class="text-sm" style="color: rgb(var(--color-text-secondary)); font-family: 'Cairo', sans-serif;">
                {{ product.category }}
              </p>
            </div>
            <div class="text-left">
              <p class="font-bold text-red-500" style="font-family: 'Cairo', sans-serif;">
                {{ product.stock }} متبقي
              </p>
              <p class="text-sm" style="color: rgb(var(--color-text-secondary)); font-family: 'Cairo', sans-serif;">
                {{ formatCurrency(product.price) }}
              </p>
            </div>
          </div>
        </div>

        <div v-else class="empty-state">
          <p style="font-family: 'Cairo', sans-serif;">جميع المنتجات متوفرة بكمية جيدة</p>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <NuxtLink to="/admin/products" class="action-card">
        <div class="action-icon bg-gradient-to-br from-blue-500 to-blue-600">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        </div>
        <h3 class="text-xl font-bold mb-3" style="color: rgb(var(--color-text-primary)); font-family: 'Cairo', sans-serif;">
          إدارة المنتجات
        </h3>
        <p style="color: rgb(var(--color-text-secondary)); font-family: 'Cairo', sans-serif;">
          إضافة وتعديل وتنظيم كتالوج المنتجات الخاص بك
        </p>
      </NuxtLink>

      <NuxtLink to="/admin/orders" class="action-card">
        <div class="action-icon bg-gradient-to-br from-green-500 to-green-600">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        </div>
        <h3 class="text-xl font-bold mb-3" style="color: rgb(var(--color-text-primary)); font-family: 'Cairo', sans-serif;">
          معالجة الطلبات
        </h3>
        <p style="color: rgb(var(--color-text-secondary)); font-family: 'Cairo', sans-serif;">
          عرض وإدارة طلبات العملاء
        </p>
      </NuxtLink>

      <NuxtLink to="/admin/users" class="action-card">
        <div class="action-icon bg-gradient-to-br from-purple-500 to-purple-600">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
        </div>
        <h3 class="text-xl font-bold mb-3" style="color: rgb(var(--color-text-primary)); font-family: 'Cairo', sans-serif;">
          إدارة المستخدمين
        </h3>
        <p style="color: rgb(var(--color-text-secondary)); font-family: 'Cairo', sans-serif;">
          عرض وإدارة حسابات المستخدمين
        </p>
      </NuxtLink>
    </div>
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'admin',
  middleware: 'admin'
});

// SEO Meta
useHead({
  title: 'لوحة المعلومات الرئيسية - بنتاكون',
  meta: [
    { name: 'description', content: 'لوحة المعلومات الرئيسية لإدارة منصة التجارة الإلكترونية الخاصة بك.' }
  ]
});

// Admin store
const { formatCurrency } = useAdminStore()

// Mock data with Arabic names
const stats = reactive({
  totalUsers: 1247,
  totalProducts: 156,
  totalOrders: 892,
  totalRevenue: 45678.90
});

const recentOrders = reactive([
  { id: 1001, customerName: 'أحمد محمد', total: 299.99, status: 'pending' },
  { id: 1002, customerName: 'فاطمة علي', total: 149.50, status: 'processing' },
  { id: 1003, customerName: 'محمد حسن', total: 89.99, status: 'completed' },
  { id: 1004, customerName: 'عائشة أحمد', total: 199.99, status: 'pending' },
  { id: 1005, customerName: 'عبدالله سالم', total: 349.99, status: 'processing' }
]);

const lowStockProducts = reactive([
  { id: 1, name: 'لابتوب الألعاب المتقدم', category: 'إلكترونيات', stock: 2, price: 1299.99 },
  { id: 2, name: 'سماعات لاسلكية', category: 'إلكترونيات', stock: 5, price: 199.99 },
  { id: 3, name: 'ساعة ذكية', category: 'إلكترونيات', stock: 3, price: 299.99 }
]);

const getStatusClass = (status: string) => {
  const statusClasses = {
    pending: 'status-pending',
    processing: 'status-processing',
    completed: 'status-completed',
    cancelled: 'status-cancelled'
  };
  return statusClasses[status as keyof typeof statusClasses] || 'status-default';
};

const getStatusLabel = (status: string) => {
  const statusLabels = {
    pending: 'قيد الانتظار',
    processing: 'قيد المعالجة',
    completed: 'مكتمل',
    cancelled: 'ملغي'
  };
  return statusLabels[status as keyof typeof statusLabels] || status;
};
</script>

<style scoped>
.admin-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--glass-shadow);
  padding: 2rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideInUp 0.6s ease-out;
}

.admin-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(var(--color-primary), 0.15);
  border-color: rgba(147, 51, 234, 0.3);
}

.order-item,
.product-item {
  @apply flex items-center justify-between p-4 rounded-xl transition-all duration-300;
  background: rgba(147, 51, 234, 0.05);
  border: 1px solid var(--glass-border);
}

.order-item:hover,
.product-item:hover {
  background: rgba(147, 51, 234, 0.1);
  transform: translateX(-4px);
  box-shadow: 0 4px 16px rgba(var(--color-primary), 0.1);
}

.status-badge {
  @apply px-3 py-1 rounded-full text-xs font-semibold;
  font-family: 'Cairo', sans-serif;
}

.status-pending {
  background: rgba(245, 158, 11, 0.2);
  color: rgb(245, 158, 11);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-processing {
  background: rgba(59, 130, 246, 0.2);
  color: rgb(59, 130, 246);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.status-completed {
  background: rgba(34, 197, 94, 0.2);
  color: rgb(34, 197, 94);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-cancelled {
  background: rgba(239, 68, 68, 0.2);
  color: rgb(239, 68, 68);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-default {
  background: rgba(107, 114, 128, 0.2);
  color: rgb(107, 114, 128);
  border: 1px solid rgba(107, 114, 128, 0.3);
}

.empty-state {
  @apply text-center py-12;
  color: rgb(var(--color-text-muted));
}

.action-card {
  @apply text-center transition-all duration-500 cursor-pointer;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--glass-shadow);
  padding: 2.5rem;
  animation: slideInUp 0.6s ease-out;
  position: relative;
  overflow: hidden;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.05), rgba(59, 130, 246, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-card:hover::before {
  opacity: 1;
}

.action-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(var(--color-primary), 0.2);
  border-color: rgba(147, 51, 234, 0.4);
}

.action-icon {
  @apply w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  animation: iconFloat 3s ease-in-out infinite;
  position: relative;
  z-index: 10;
}

.action-card:hover .action-icon {
  animation-play-state: paused;
  transform: scale(1.1) rotate(5deg);
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .admin-card,
  .action-card {
    padding: 1.5rem;
  }

  .order-item,
  .product-item {
    @apply p-3;
  }

  .action-icon {
    @apply w-12 h-12 mb-4;
  }
}

/* RTL Support */
[dir="rtl"] .order-item:hover,
[dir="rtl"] .product-item:hover {
  transform: translateX(4px);
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .admin-card,
  .action-card,
  .action-icon {
    animation: none;
    transition: none;
  }

  .admin-card:hover,
  .action-card:hover {
    transform: none;
  }
}
</style>
