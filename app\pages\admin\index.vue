<template>
  <div>
    <!-- <PERSON>er -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">Admin Dashboard</h1>
      <p class="text-lg text-gray-600">Overview of your e-commerce platform</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Users -->
      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Users</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalUsers }}</p>
          </div>
        </div>
      </div>

      <!-- Total Products -->
      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM6 9a1 1 0 112 0v6a1 1 0 11-2 0V9zm6 0a1 1 0 112 0v6a1 1 0 11-2 0V9z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Products</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalProducts }}</p>
          </div>
        </div>
      </div>

      <!-- Total Orders -->
      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Orders</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalOrders }}</p>
          </div>
        </div>
      </div>

      <!-- Revenue -->
      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Revenue</p>
            <p class="text-2xl font-bold text-gray-900">${{ stats.totalRevenue.toFixed(2) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Recent Orders -->
      <div class="card">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-gray-900">Recent Orders</h2>
          <NuxtLink to="/admin/orders" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
            View All
          </NuxtLink>
        </div>

        <div v-if="recentOrders.length > 0" class="space-y-4">
          <div 
            v-for="order in recentOrders" 
            :key="order.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div>
              <p class="font-medium text-gray-900">Order #{{ order.id }}</p>
              <p class="text-sm text-gray-600">{{ order.customerName }}</p>
            </div>
            <div class="text-right">
              <p class="font-medium text-gray-900">${{ order.total.toFixed(2) }}</p>
              <span :class="getStatusClass(order.status)" class="badge text-xs">
                {{ order.status }}
              </span>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-8 text-gray-500">
          No recent orders
        </div>
      </div>

      <!-- Low Stock Products -->
      <div class="card">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-gray-900">Low Stock Alert</h2>
          <NuxtLink to="/admin/products" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
            Manage Products
          </NuxtLink>
        </div>

        <div v-if="lowStockProducts.length > 0" class="space-y-4">
          <div 
            v-for="product in lowStockProducts" 
            :key="product.id"
            class="flex items-center justify-between p-3 bg-red-50 rounded-lg"
          >
            <div>
              <p class="font-medium text-gray-900">{{ product.name }}</p>
              <p class="text-sm text-gray-600">{{ product.category }}</p>
            </div>
            <div class="text-right">
              <p class="font-medium text-red-600">{{ product.stock }} left</p>
              <p class="text-sm text-gray-600">${{ product.price.toFixed(2) }}</p>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-8 text-gray-500">
          All products are well stocked
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <NuxtLink to="/admin/products" class="card hover:shadow-md transition-shadow text-center">
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5z" clip-rule="evenodd" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Manage Products</h3>
        <p class="text-gray-600">Add, edit, and organize your product catalog</p>
      </NuxtLink>

      <NuxtLink to="/admin/orders" class="card hover:shadow-md transition-shadow text-center">
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Process Orders</h3>
        <p class="text-gray-600">View and manage customer orders</p>
      </NuxtLink>

      <NuxtLink to="/admin/users" class="card hover:shadow-md transition-shadow text-center">
        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Manage Users</h3>
        <p class="text-gray-600">View and manage user accounts</p>
      </NuxtLink>
    </div>
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'dashboard',
  middleware: 'admin'
});

// SEO Meta
useHead({
  title: 'Admin Dashboard - E-Commerce Store',
  meta: [
    { name: 'description', content: 'Admin dashboard for managing your e-commerce platform.' }
  ]
});

// Mock data - TODO: backend - Replace with real dashboard API
const stats = reactive({
  totalUsers: 1247,
  totalProducts: 156,
  totalOrders: 892,
  totalRevenue: 45678.90
});

const recentOrders = reactive([
  { id: 1001, customerName: 'John Doe', total: 299.99, status: 'pending' },
  { id: 1002, customerName: 'Jane Smith', total: 149.50, status: 'processing' },
  { id: 1003, customerName: 'Bob Johnson', total: 89.99, status: 'completed' },
  { id: 1004, customerName: 'Alice Brown', total: 199.99, status: 'pending' },
  { id: 1005, customerName: 'Charlie Wilson', total: 349.99, status: 'processing' }
]);

const lowStockProducts = reactive([
  { id: 1, name: 'Gaming Laptop Pro', category: 'electronics', stock: 2, price: 1299.99 },
  { id: 2, name: 'Wireless Headphones', category: 'electronics', stock: 5, price: 199.99 },
  { id: 3, name: 'Smart Watch', category: 'electronics', stock: 3, price: 299.99 }
]);

const getStatusClass = (status: string) => {
  const statusClasses = {
    pending: 'status-pending',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'status-completed',
    cancelled: 'bg-red-100 text-red-800'
  };
  return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800';
};
</script>
