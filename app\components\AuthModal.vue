<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4" dir="rtl">
    <!-- Backdrop -->
    <div class="absolute inset-0 bg-black/60 backdrop-blur-sm" @click="$emit('close')"></div>

    <!-- Modal -->
    <div class="relative bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-8 w-full max-w-md animate-fade-in">
      <!-- Header -->
      <div class="flex items-center justify-between mb-8">
        <h2 class="section-title text-2xl">{{ isLogin ? 'تسجيل الدخول' : 'إنشاء حساب' }}</h2>
        <button @click="$emit('close')" class="text-theme-muted hover:text-white-force transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Name Field (Signup only) -->
        <div v-if="!isLogin">
          <label class="block text-white-force text-sm font-medium mb-2">الاسم الكامل</label>
          <input
            v-model="form.name"
            type="text"
            class="input-field"
            placeholder="أدخل اسمك الكامل"
            dir="rtl"
            required
          >
        </div>

        <!-- Email Field -->
        <div>
          <label class="block text-white-force text-sm font-medium mb-2">البريد الإلكتروني</label>
          <input
            v-model="form.email"
            type="email"
            class="input-field"
            placeholder="أدخل بريدك الإلكتروني"
            dir="rtl"
            required
          >
        </div>

        <!-- Password Field -->
        <div>
          <label class="block text-white-force text-sm font-medium mb-2">كلمة المرور</label>
          <div class="relative">
            <input
              v-model="form.password"
              :type="showPassword ? 'text' : 'password'"
              class="input-field pl-12"
              placeholder="أدخل كلمة المرور"
              dir="rtl"
              required
            >
            <button
              type="button"
              @click="showPassword = !showPassword"
              class="absolute right-3 top-1/2 transform -translate-y-1/2 text-theme-muted hover:text-white-force transition-colors"
            >
              <svg v-if="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Confirm Password (Signup only) -->
        <div v-if="!isLogin">
          <label class="block text-white-force text-sm font-medium mb-2">تأكيد كلمة المرور</label>
          <input
            v-model="form.confirmPassword"
            type="password"
            class="input-field"
            placeholder="أعد إدخال كلمة المرور"
            dir="rtl"
            required
          >
        </div>

        <!-- Phone (Signup only) -->
        <div v-if="!isLogin">
          <label class="block text-white-force text-sm font-medium mb-2">رقم الهاتف</label>
          <input
            v-model="form.phone"
            type="tel"
            class="input-field"
            placeholder="+966501234567"
            dir="rtl"
          >
        </div>

        <!-- Remember Me (Login only) -->
        <div v-if="isLogin" class="flex items-center" dir="rtl">
          <input
            v-model="form.rememberMe"
            type="checkbox"
            id="remember"
            class="w-4 h-4 text-theme-accent bg-theme-surface border-theme-light rounded focus:ring-theme-accent focus:ring-2"
          >
          <label for="remember" class="ml-2 text-sm text-theme-secondary">تذكرني</label>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
          <p class="text-red-400 text-sm">{{ error }}</p>
        </div>

        <!-- Submit Button -->
        <button 
          type="submit" 
          :disabled="isLoading"
          class="btn-primary w-full py-3 flex items-center justify-center space-x-2 space-x-reverse"
        >
          <svg v-if="isLoading" class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>{{ isLoading ? 'جاري المعالجة...' : (isLogin ? 'تسجيل الدخول' : 'إنشاء الحساب') }}</span>
        </button>
      </form>

      <!-- Switch Mode -->
      <div class="mt-6 text-center">
        <p class="text-theme-secondary text-sm">
          {{ isLogin ? 'ليس لديك حساب؟' : 'لديك حساب بالفعل؟' }}
          <button 
            @click="toggleMode" 
            class="text-theme-accent hover:text-theme-primary font-medium transition-colors mr-1"
          >
            {{ isLogin ? 'إنشاء حساب جديد' : 'تسجيل الدخول' }}
          </button>
        </p>
      </div>

      <!-- Demo Notice -->
      <div class="mt-6 p-3 bg-blue-500/20 border border-blue-500/30 rounded-lg">
        <p class="text-blue-400 text-xs text-center">
          <strong>وضع التطوير:</strong> يمكنك استخدام أي بيانات لتسجيل الدخول
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['close', 'success'])

// Reactive state
const isLogin = ref(true)
const showPassword = ref(false)
const isLoading = ref(false)
const error = ref('')

const form = ref({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  phone: '',
  rememberMe: false
})

// Auth store
const { login, register } = useAuthStore()

// Methods
const toggleMode = () => {
  isLogin.value = !isLogin.value
  error.value = ''
  // Reset form
  form.value = {
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    rememberMe: false
  }
}

const validateForm = () => {
  if (!form.value.email || !form.value.password) {
    error.value = 'يرجى ملء جميع الحقول المطلوبة'
    return false
  }

  if (!isLogin.value) {
    if (!form.value.name) {
      error.value = 'يرجى إدخال الاسم الكامل'
      return false
    }
    
    if (form.value.password !== form.value.confirmPassword) {
      error.value = 'كلمات المرور غير متطابقة'
      return false
    }
    
    if (form.value.password.length < 6) {
      error.value = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
      return false
    }
  }

  return true
}

const handleSubmit = async () => {
  error.value = ''

  if (!validateForm()) return

  isLoading.value = true

  try {
    if (isLogin.value) {
      await login(form.value.email, form.value.password, form.value.rememberMe)
    } else {
      await register({
        name: form.value.name,
        email: form.value.email,
        password: form.value.password,
        phone: form.value.phone
      })
    }

    emit('success')
    emit('close')

    // Navigate to the intended page or home
    const route = useRoute()
    const router = useRouter()

    // If user was trying to access a protected route, redirect there
    if (route.query.redirect) {
      await router.push(route.query.redirect as string)
    } else if (route.query.login === 'required') {
      // Remove the login query parameter and stay on current page
      await router.replace({ query: {} })
    }

  } catch (err: any) {
    error.value = err.message || 'حدث خطأ أثناء المعالجة'
  } finally {
    isLoading.value = false
  }
}
</script>
