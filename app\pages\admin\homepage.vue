<template>
  <div>
    <!-- <PERSON>er -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">Homepage Management</h1>
      <p class="text-lg text-gray-600">Manage homepage content and featured items</p>
    </div>

    <div class="space-y-8">
      <!-- Banner Slider Management -->
      <div class="card">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-900">Banner Slider</h2>
          <button 
            @click="addBanner"
            class="btn-primary"
          >
            Add Banner
          </button>
        </div>

        <div v-if="banners.length > 0" class="space-y-4">
          <div 
            v-for="(banner, index) in banners" 
            :key="banner.id"
            class="border border-gray-200 rounded-lg p-4"
          >
            <div class="flex items-center justify-between mb-3">
              <h3 class="font-medium text-gray-900">Banner {{ index + 1 }}</h3>
              <div class="flex items-center space-x-2">
                <span 
                  :class="banner.isActive ? 'status-completed' : 'bg-gray-100 text-gray-800'"
                  class="badge text-xs"
                >
                  {{ banner.isActive ? 'Active' : 'Inactive' }}
                </span>
                <button 
                  @click="editBanner(banner)"
                  class="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Edit
                </button>
                <button 
                  @click="deleteBanner(banner.id)"
                  class="text-red-600 hover:text-red-800 text-sm"
                >
                  Delete
                </button>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <img 
                  :src="banner.image" 
                  :alt="banner.title"
                  class="w-full h-24 object-cover rounded-lg bg-gray-200"
                />
              </div>
              <div class="md:col-span-2">
                <h4 class="font-medium text-gray-900">{{ banner.title }}</h4>
                <p class="text-sm text-gray-600 mt-1">{{ banner.subtitle }}</p>
                <p class="text-xs text-gray-500 mt-2">
                  Button: {{ banner.buttonText }} → {{ banner.buttonLink }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-8 text-gray-500">
          No banners configured. Add your first banner to get started.
        </div>
      </div>

      <!-- Featured Products Management -->
      <div class="card">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-900">Featured Products</h2>
          <button 
            @click="manageFeaturedProducts"
            class="btn-primary"
          >
            Manage Featured
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div 
            v-for="product in featuredProducts" 
            :key="product.id"
            class="border border-gray-200 rounded-lg p-4"
          >
            <img 
              :src="product.image || '/images/placeholder.jpg'" 
              :alt="product.name"
              class="w-full h-32 object-cover rounded-lg bg-gray-200 mb-3"
            />
            <h3 class="font-medium text-gray-900">{{ product.name }}</h3>
            <p class="text-sm text-gray-600">${{ product.price.toFixed(2) }}</p>
            <button 
              @click="removeFeaturedProduct(product.id)"
              class="mt-2 text-red-600 hover:text-red-800 text-sm"
            >
              Remove from Featured
            </button>
          </div>
        </div>

        <div v-if="featuredProducts.length === 0" class="text-center py-8 text-gray-500">
          No featured products selected.
        </div>
      </div>

      <!-- Homepage Sections -->
      <div class="card">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Homepage Sections</h2>

        <div class="space-y-6">
          <!-- Hero Section -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="font-medium text-gray-900">Hero Section</h3>
              <div class="flex items-center space-x-2">
                <label class="flex items-center">
                  <input 
                    v-model="sections.hero.enabled" 
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">Enabled</span>
                </label>
              </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                <input 
                  v-model="sections.hero.title" 
                  type="text" 
                  class="input-field"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Subtitle</label>
                <input 
                  v-model="sections.hero.subtitle" 
                  type="text" 
                  class="input-field"
                />
              </div>
            </div>
          </div>

          <!-- Categories Section -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="font-medium text-gray-900">Categories Section</h3>
              <div class="flex items-center space-x-2">
                <label class="flex items-center">
                  <input 
                    v-model="sections.categories.enabled" 
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">Enabled</span>
                </label>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Section Title</label>
              <input 
                v-model="sections.categories.title" 
                type="text" 
                class="input-field"
              />
            </div>
          </div>

          <!-- Featured Products Section -->
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="font-medium text-gray-900">Featured Products Section</h3>
              <div class="flex items-center space-x-2">
                <label class="flex items-center">
                  <input 
                    v-model="sections.featured.enabled" 
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">Enabled</span>
                </label>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Section Title</label>
              <input 
                v-model="sections.featured.title" 
                type="text" 
                class="input-field"
              />
            </div>
          </div>
        </div>

        <div class="flex justify-end mt-6 pt-6 border-t border-gray-200">
          <button 
            @click="saveHomepageSettings"
            class="btn-primary"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>

    <!-- Banner Modal -->
    <BannerModal 
      v-if="showBannerModal"
      :banner="editingBanner"
      :is-editing="!!editingBanner"
      @close="closeBannerModal"
      @success="handleBannerSuccess"
    />
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'dashboard',
  middleware: 'admin'
});

// SEO Meta
useHead({
  title: 'Homepage Management - Admin Dashboard',
  meta: [
    { name: 'description', content: 'Manage homepage content, banners, and featured products.' }
  ]
});

// Mock data - TODO: backend - Replace with real homepage API
const banners = reactive([
  {
    id: 1,
    title: 'Summer Sale',
    subtitle: 'Up to 50% off on selected items',
    image: '/images/banner1.jpg',
    buttonText: 'Shop Now',
    buttonLink: '/shop',
    isActive: true
  },
  {
    id: 2,
    title: 'New Arrivals',
    subtitle: 'Check out our latest products',
    image: '/images/banner2.jpg',
    buttonText: 'Explore',
    buttonLink: '/shop?sort=newest',
    isActive: true
  }
]);

const featuredProducts = reactive([
  { id: 1, name: 'Gaming Laptop Pro', price: 1299.99, image: '/images/laptop.jpg' },
  { id: 2, name: 'Wireless Headphones', price: 199.99, image: '/images/headphones.jpg' },
  { id: 3, name: 'Smart Watch', price: 299.99, image: '/images/watch.jpg' }
]);

const sections = reactive({
  hero: {
    enabled: true,
    title: 'Welcome to Our Store',
    subtitle: 'Discover amazing products at great prices'
  },
  categories: {
    enabled: true,
    title: 'Shop by Category'
  },
  featured: {
    enabled: true,
    title: 'Featured Products'
  }
});

// Modal state
const showBannerModal = ref(false);
const editingBanner = ref(null);

// Methods
const addBanner = () => {
  editingBanner.value = null;
  showBannerModal.value = true;
};

const editBanner = (banner: any) => {
  editingBanner.value = banner;
  showBannerModal.value = true;
};

const deleteBanner = (bannerId: number) => {
  if (confirm('Are you sure you want to delete this banner?')) {
    const index = banners.findIndex(b => b.id === bannerId);
    if (index > -1) {
      banners.splice(index, 1);
    }
  }
};

const closeBannerModal = () => {
  showBannerModal.value = false;
  editingBanner.value = null;
};

const handleBannerSuccess = () => {
  closeBannerModal();
  // TODO: backend - Refresh banners data
};

const manageFeaturedProducts = () => {
  // TODO: Implement featured products selection modal
  alert('Featured products management modal would open here');
};

const removeFeaturedProduct = (productId: number) => {
  const index = featuredProducts.findIndex(p => p.id === productId);
  if (index > -1) {
    featuredProducts.splice(index, 1);
  }
};

const saveHomepageSettings = async () => {
  // TODO: backend - Implement homepage settings save API
  console.log('Saving homepage settings:', sections);
  alert('Homepage settings saved successfully!');
};
</script>
