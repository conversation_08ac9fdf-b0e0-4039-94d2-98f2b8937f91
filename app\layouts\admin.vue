<template>
  <div class="admin-layout" dir="rtl">
    <!-- Mobile Menu Overlay -->
    <div
      v-if="isMobileMenuOpen"
      class="mobile-overlay"
      @click="closeMobileMenu"
    />

    <!-- Sidebar -->
    <aside
      class="admin-sidebar"
      :class="{
        'sidebar-collapsed': isSidebarCollapsed && !isMobile,
        'sidebar-mobile-open': isMobileMenuOpen && isMobile
      }"
    >
      <!-- Sidebar Header -->
      <div class="sidebar-header">
        <div class="sidebar-logo">
          <div class="logo-icon">
            <svg class="w-8 h-8 text-theme-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <Transition name="sidebar-text">
            <div v-if="!isSidebarCollapsed || isMobile" class="logo-text">
              <h1 class="text-lg font-bold text-theme-primary arabic-text">لوحة الإدارة</h1>
              <p class="text-xs text-theme-muted arabic-text">متجر بنتاكون</p>
            </div>
          </Transition>
        </div>

        <!-- Collapse Button (Desktop) -->
        <button
          v-if="!isMobile"
          @click="toggleSidebar"
          class="sidebar-collapse-btn"
        >
          <svg class="w-5 h-5 transition-transform" :class="{ 'rotate-180': isSidebarCollapsed }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      </div>

      <!-- Navigation -->
      <nav class="sidebar-nav">
        <div class="nav-section">
          <!-- Dashboard -->
          <NuxtLink
            to="/admin"
            class="nav-item"
            :class="{ 'nav-item-active': $route.path === '/admin' }"
            @click="closeMobileMenu"
          >
            <div class="nav-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <Transition name="sidebar-text">
              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text arabic-text">لوحة المعلومات</span>
            </Transition>
          </NuxtLink>

          <!-- Users & Workers -->
          <NuxtLink
            to="/admin/users"
            class="nav-item"
            :class="{ 'nav-item-active': $route.path.startsWith('/admin/users') }"
            @click="closeMobileMenu"
          >
            <div class="nav-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <Transition name="sidebar-text">
              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text arabic-text">المستخدمين والعمال</span>
            </Transition>
          </NuxtLink>

          <!-- Products -->
          <NuxtLink
            to="/admin/products"
            class="nav-item"
            :class="{ 'nav-item-active': $route.path.startsWith('/admin/products') }"
            @click="closeMobileMenu"
          >
            <div class="nav-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <Transition name="sidebar-text">
              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text arabic-text">المنتجات</span>
            </Transition>
          </NuxtLink>

          <!-- Orders -->
          <NuxtLink
            to="/admin/orders"
            class="nav-item"
            :class="{ 'nav-item-active': $route.path.startsWith('/admin/orders') }"
            @click="closeMobileMenu"
          >
            <div class="nav-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <Transition name="sidebar-text">
              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text arabic-text">الطلبات</span>
            </Transition>
          </NuxtLink>

          <!-- Wallet Transactions -->
          <NuxtLink
            to="/admin/wallet"
            class="nav-item"
            :class="{ 'nav-item-active': $route.path.startsWith('/admin/wallet') }"
            @click="closeMobileMenu"
          >
            <div class="nav-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <Transition name="sidebar-text">
              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text arabic-text">معاملات المحفظة</span>
            </Transition>
          </NuxtLink>

          <!-- Currency Management -->
          <NuxtLink
            to="/admin/currencies"
            class="nav-item"
            :class="{ 'nav-item-active': $route.path.startsWith('/admin/currencies') }"
            @click="closeMobileMenu"
          >
            <div class="nav-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <Transition name="sidebar-text">
              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text arabic-text">إدارة العملات</span>
            </Transition>
          </NuxtLink>

          <!-- Settings -->
          <NuxtLink
            to="/admin/settings"
            class="nav-item"
            :class="{ 'nav-item-active': $route.path.startsWith('/admin/settings') }"
            @click="closeMobileMenu"
          >
            <div class="nav-icon">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <Transition name="sidebar-text">
              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text arabic-text">الإعدادات</span>
            </Transition>
          </NuxtLink>
        </div>
      </nav>
    </aside>

    <!-- Main Content Area -->
    <div class="admin-main" :class="{ 'main-expanded': isSidebarCollapsed && !isMobile }">
      <!-- Top Bar -->
      <header class="admin-topbar">
        <div class="topbar-left">
          <!-- Mobile Menu Button -->
          <button
            v-if="isMobile"
            @click="toggleMobileMenu"
            class="mobile-menu-btn"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>

          <!-- Page Title -->
          <div class="page-title">
            <h2 class="text-xl font-semibold text-theme-primary arabic-text">{{ pageTitle }}</h2>
          </div>
        </div>

        <div class="topbar-right">
          <!-- Notifications -->
          <button class="topbar-btn" title="الإشعارات">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12M1.01 11.88l3.12 3.12" />
            </svg>
            <span class="notification-badge">3</span>
          </button>

          <!-- User Menu -->
          <div class="user-menu">
            <div class="user-info">
              <div class="user-avatar">
                <span class="text-sm font-medium">{{ userInitials }}</span>
              </div>
              <div class="user-details">
                <div class="user-name arabic-text">{{ userDisplayName }}</div>
                <div class="user-role arabic-text">{{ userRoleLabel }}</div>
              </div>
            </div>
            
            <button @click="handleLogout" class="logout-btn" title="تسجيل الخروج">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        </div>
      </header>

      <!-- Page Content -->
      <main class="admin-content">
        <slot />
      </main>
    </div>

    <!-- Notifications -->
    <NotificationContainer />
  </div>
</template>

<script setup lang="ts">
const { userDisplayName, userRole, logout } = useAuthStore();

// Responsive state
const isMobile = ref(false);
const isSidebarCollapsed = ref(false);
const isMobileMenuOpen = ref(false);

// Page title
const route = useRoute();
const pageTitle = computed(() => {
  const titles: Record<string, string> = {
    '/admin': 'لوحة المعلومات',
    '/admin/users': 'المستخدمين والعمال',
    '/admin/products': 'المنتجات',
    '/admin/orders': 'الطلبات',
    '/admin/wallet': 'معاملات المحفظة',
    '/admin/currencies': 'إدارة العملات',
    '/admin/settings': 'الإعدادات'
  };
  return titles[route.path] || 'لوحة الإدارة';
});

// User info
const userInitials = computed(() => {
  return userDisplayName.value
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
});

const userRoleLabel = computed(() => {
  const labels = {
    admin: 'مدير',
    worker: 'موظف',
    distributor: 'موزع',
    user: 'عميل'
  };
  return labels[userRole.value as keyof typeof labels] || 'مستخدم';
});

// Methods
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value;
};

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value;
};

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false;
};

const handleLogout = async () => {
  await logout();
};

// Handle responsive behavior
const handleResize = () => {
  isMobile.value = window.innerWidth < 768;
  if (!isMobile.value) {
    isMobileMenuOpen.value = false;
  }
};

onMounted(() => {
  handleResize();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

// Middleware check
definePageMeta({
  middleware: ['auth', 'admin']
});
</script>

<style scoped>
.admin-layout {
  @apply min-h-screen flex;
  background: linear-gradient(135deg, rgb(var(--color-background)), rgb(var(--color-surface)));
  font-family: 'Cairo', 'Inter', system-ui, sans-serif;
  direction: rtl;
}

/* Mobile Overlay */
.mobile-overlay {
  @apply fixed inset-0 z-40 md:hidden;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

/* Sidebar */
.admin-sidebar {
  @apply fixed left-0 top-0 h-full z-50;
  width: 280px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-left: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transform: translateX(-100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-sidebar.sidebar-mobile-open {
  transform: translateX(0);
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-sidebar.sidebar-collapsed {
  width: 80px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (min-width: 768px) {
  .admin-sidebar {
    @apply relative;
    transform: translateX(0);
    background: linear-gradient(180deg, var(--glass-bg), rgba(31, 41, 55, 0.7));
  }
}

.sidebar-header {
  @apply flex items-center justify-between p-6;
  border-bottom: 1px solid var(--glass-border);
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1));
}

.sidebar-logo {
  @apply flex items-center gap-3;
  animation: slideInDown 0.6s ease-out;
}

.logo-icon {
  @apply flex-shrink-0 p-2 rounded-xl;
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  box-shadow: 0 4px 16px rgba(var(--color-primary), 0.3);
  animation: pulse 2s infinite;
}

.logo-text {
  @apply overflow-hidden;
}

.logo-text h1 {
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-collapse-btn {
  @apply p-2 rounded-lg transition-all duration-300;
  color: rgb(var(--color-text-muted));
  background: rgba(147, 51, 234, 0.1);
  border: 1px solid var(--glass-border);
}

.sidebar-collapse-btn:hover {
  color: rgb(var(--color-primary));
  background: rgba(147, 51, 234, 0.2);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(var(--color-primary), 0.2);
}

.sidebar-nav {
  @apply flex-1 overflow-y-auto p-4;
  scrollbar-width: thin;
  scrollbar-color: rgba(147, 51, 234, 0.3) transparent;
}

.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(147, 51, 234, 0.3);
  border-radius: 2px;
}

.nav-section {
  @apply space-y-2;
}

.nav-item {
  @apply flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 relative overflow-hidden;
  color: rgb(var(--color-text-secondary));
  border: 1px solid transparent;
  font-family: 'Cairo', sans-serif;
  text-decoration: none;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 0.75rem;
}

.nav-item:hover::before {
  opacity: 1;
}

.nav-item:hover {
  color: rgb(var(--color-text-primary));
  transform: translateX(-4px);
  border-color: var(--glass-border-hover);
  box-shadow: 0 4px 16px rgba(var(--color-primary), 0.1);
}

.nav-item-active {
  color: rgb(var(--color-text-primary));
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(59, 130, 246, 0.2));
  border-color: rgb(var(--color-primary));
  border-right: 3px solid rgb(var(--color-primary));
  box-shadow: 0 4px 16px rgba(var(--color-primary), 0.2);
  animation: glow 2s ease-in-out infinite alternate;
}

.nav-icon {
  @apply flex-shrink-0 relative z-10;
  transition: transform 0.3s ease;
}

.nav-item:hover .nav-icon {
  transform: scale(1.1);
}

.nav-text {
  @apply overflow-hidden whitespace-nowrap relative z-10;
  font-weight: 500;
}

/* Main Content */
.admin-main {
  @apply flex-1 flex flex-col;
  margin-left: 0;
  transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (min-width: 768px) {
  .admin-main {
    margin-left: 280px;
  }

  .admin-main.main-expanded {
    margin-left: 80px;
  }
}

/* Top Bar */
.admin-topbar {
  @apply flex items-center justify-between px-6 py-4;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.topbar-left {
  @apply flex items-center gap-4;
  animation: slideInLeft 0.6s ease-out;
}

.mobile-menu-btn {
  @apply p-3 rounded-xl transition-all duration-300;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1));
  color: rgb(var(--color-primary));
  border: 1px solid var(--glass-border);
}

.mobile-menu-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(var(--color-primary), 0.2);
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.2), rgba(59, 130, 246, 0.2));
}

.page-title {
  @apply flex items-center;
}

.page-title h2 {
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.topbar-right {
  @apply flex items-center gap-4;
  animation: slideInRight 0.6s ease-out;
}

.topbar-btn {
  @apply relative p-3 rounded-xl transition-all duration-300;
  background: rgba(147, 51, 234, 0.1);
  color: rgb(var(--color-text-muted));
  border: 1px solid var(--glass-border);
}

.topbar-btn:hover {
  color: rgb(var(--color-primary));
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(var(--color-primary), 0.2);
  background: rgba(147, 51, 234, 0.2);
}

.notification-badge {
  @apply absolute -top-1 -right-1 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center;
  background: linear-gradient(135deg, rgb(var(--color-error)), rgb(var(--color-accent)));
  box-shadow: 0 2px 8px rgba(var(--color-error), 0.4);
  animation: bounce 2s infinite;
}

.user-menu {
  @apply flex items-center gap-3;
}

.user-info {
  @apply flex items-center gap-3 p-2 rounded-xl transition-all duration-300;
  background: rgba(147, 51, 234, 0.1);
  border: 1px solid var(--glass-border);
}

.user-info:hover {
  background: rgba(147, 51, 234, 0.2);
  transform: scale(1.02);
  box-shadow: 0 4px 16px rgba(var(--color-primary), 0.1);
}

.user-avatar {
  @apply w-10 h-10 text-white rounded-full flex items-center justify-center font-bold;
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  box-shadow: 0 4px 12px rgba(var(--color-primary), 0.3);
  animation: pulse 3s infinite;
}

.user-details {
  @apply hidden sm:block;
}

.user-name {
  @apply text-sm font-semibold;
  color: rgb(var(--color-text-primary));
}

.user-role {
  @apply text-xs;
  color: rgb(var(--color-text-muted));
}

.logout-btn {
  @apply p-3 rounded-xl transition-all duration-300;
  background: rgba(239, 68, 68, 0.1);
  color: rgb(var(--color-error));
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.2);
}

/* Content */
.admin-content {
  @apply flex-1 p-6 overflow-y-auto;
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.5), rgba(31, 41, 55, 0.3));
  scrollbar-width: thin;
  scrollbar-color: rgba(147, 51, 234, 0.3) transparent;
}

.admin-content::-webkit-scrollbar {
  width: 6px;
}

.admin-content::-webkit-scrollbar-track {
  background: transparent;
}

.admin-content::-webkit-scrollbar-thumb {
  background: rgba(147, 51, 234, 0.3);
  border-radius: 3px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 4px 16px rgba(var(--color-primary), 0.2);
  }
  to {
    box-shadow: 0 4px 24px rgba(var(--color-primary), 0.4);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Transitions */
.sidebar-text-enter-active,
.sidebar-text-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-text-enter-from,
.sidebar-text-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

/* Mobile Responsive */
@media (max-width: 767px) {
  .admin-content {
    @apply p-4;
  }

  .topbar-left {
    @apply gap-2;
  }

  .topbar-right {
    @apply gap-2;
  }

  .user-info {
    @apply gap-2 p-1;
  }

  .user-avatar {
    @apply w-8 h-8;
  }

  .admin-topbar {
    @apply px-4 py-3;
  }

  .sidebar-header {
    @apply p-4;
  }

  .mobile-menu-btn {
    @apply p-2;
  }

  .topbar-btn {
    @apply p-2;
  }

  .logout-btn {
    @apply p-2;
  }
}

/* RTL Support */
[dir="rtl"] .nav-item:hover {
  transform: translateX(4px);
}

[dir="rtl"] .nav-item-active {
  border-right: none;
  border-left: 3px solid rgb(var(--color-primary));
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg,
    rgba(147, 51, 234, 0.1) 0%,
    rgba(147, 51, 234, 0.2) 50%,
    rgba(147, 51, 234, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
