// TODO: backend - Replace with real database integration
export default defineEventHandler(async (event) => {
  // Mock categories data
  const categories = [
    {
      id: 1,
      name: "Electronics",
      slug: "electronics",
      description: "Latest electronic devices and gadgets",
      image: "/images/categories/electronics.jpg",
      productCount: 150,
      isActive: true
    },
    {
      id: 2,
      name: "Home & Garden",
      slug: "home",
      description: "Everything for your home and garden",
      image: "/images/categories/home.jpg",
      productCount: 89,
      isActive: true
    },
    {
      id: 3,
      name: "Sports & Fitness",
      slug: "sports",
      description: "Sports equipment and fitness gear",
      image: "/images/categories/sports.jpg",
      productCount: 67,
      isActive: true
    },
    {
      id: 4,
      name: "Fashion",
      slug: "fashion",
      description: "Clothing and accessories",
      image: "/images/categories/fashion.jpg",
      productCount: 234,
      isActive: true
    },
    {
      id: 5,
      name: "Books & Media",
      slug: "books",
      description: "Books, movies, and digital media",
      image: "/images/categories/books.jpg",
      productCount: 45,
      isActive: false
    }
  ];

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 50));

  return {
    success: true,
    data: categories,
    total: categories.length
  };
});
