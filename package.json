{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.2", "@vueuse/core": "^13.6.0", "@vueuse/nuxt": "^13.6.0", "nuxt": "^4.0.1", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@nuxt/devtools": "^2.6.2", "@nuxt/typescript-build": "^3.0.2", "@types/node": "^24.1.0", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vue-tsc": "^3.0.4"}}