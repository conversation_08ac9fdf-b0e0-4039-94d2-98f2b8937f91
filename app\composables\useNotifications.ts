/**
 * Notifications composable for showing toast messages
 */

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
}

const notifications = ref<Notification[]>([]);

export const useNotifications = () => {
  const { generateId } = useUtils();

  /**
   * Add a new notification
   */
  const addNotification = (notification: Omit<Notification, 'id'>): string => {
    const id = generateId();
    const newNotification: Notification = {
      id,
      duration: 5000, // Default 5 seconds
      persistent: false,
      ...notification
    };

    notifications.value.push(newNotification);

    // Auto-remove notification after duration (unless persistent)
    if (!newNotification.persistent && newNotification.duration && newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  };

  /**
   * Remove a notification by ID
   */
  const removeNotification = (id: string): void => {
    const index = notifications.value.findIndex(n => n.id === id);
    if (index > -1) {
      notifications.value.splice(index, 1);
    }
  };

  /**
   * Clear all notifications
   */
  const clearNotifications = (): void => {
    notifications.value = [];
  };

  /**
   * Show success notification
   */
  const success = (title: string, message?: string, options?: Partial<Notification>): string => {
    return addNotification({
      type: 'success',
      title,
      message,
      ...options
    });
  };

  /**
   * Show error notification
   */
  const error = (title: string, message?: string, options?: Partial<Notification>): string => {
    return addNotification({
      type: 'error',
      title,
      message,
      duration: 8000, // Longer duration for errors
      ...options
    });
  };

  /**
   * Show warning notification
   */
  const warning = (title: string, message?: string, options?: Partial<Notification>): string => {
    return addNotification({
      type: 'warning',
      title,
      message,
      duration: 6000,
      ...options
    });
  };

  /**
   * Show info notification
   */
  const info = (title: string, message?: string, options?: Partial<Notification>): string => {
    return addNotification({
      type: 'info',
      title,
      message,
      ...options
    });
  };

  /**
   * Show API error notification with proper formatting
   */
  const apiError = (error: any, defaultMessage: string = 'An error occurred'): string => {
    let title = 'Error';
    let message = defaultMessage;

    if (error?.data?.message) {
      message = error.data.message;
    } else if (error?.message) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    }

    return addNotification({
      type: 'error',
      title,
      message,
      duration: 8000
    });
  };

  /**
   * Show loading notification (persistent until manually removed)
   */
  const loading = (title: string, message?: string): string => {
    return addNotification({
      type: 'info',
      title,
      message,
      persistent: true,
      duration: 0
    });
  };

  return {
    notifications: readonly(notifications),
    addNotification,
    removeNotification,
    clearNotifications,
    success,
    error,
    warning,
    info,
    apiError,
    loading
  };
};

/**
 * Global notification functions (can be used without composable)
 */
export const notify = {
  success: (title: string, message?: string) => {
    const { success } = useNotifications();
    return success(title, message);
  },
  
  error: (title: string, message?: string) => {
    const { error } = useNotifications();
    return error(title, message);
  },
  
  warning: (title: string, message?: string) => {
    const { warning } = useNotifications();
    return warning(title, message);
  },
  
  info: (title: string, message?: string) => {
    const { info } = useNotifications();
    return info(title, message);
  }
};
