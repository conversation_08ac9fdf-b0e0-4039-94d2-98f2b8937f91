<template>
  <div
    ref="containerRef"
    class="relative overflow-hidden rounded-xl shadow-2xl aspect-[2.33/1] cursor-grab active:cursor-grabbing select-none"
    style="touch-action: pan-x"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeaveContainer"
  >
    <!-- Image Slider -->
    <div
      ref="sliderRef"
      class="relative w-full h-full"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mouseleave="handleMouseLeave"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <div
        v-for="(image, index) in bannerImages"
        :key="index"
        class="absolute inset-0"
        :style="{
          opacity: getImageOpacity(index),
          transform: getImageTransform(index),
          transition: isDragging ? 'none' : 'opacity 0.7s ease-in-out, transform 0.3s ease-out'
        }"
      >
        <img
          :src="image"
          :alt="`بانر ${index + 1}`"
          class="w-full h-full object-cover pointer-events-none"
          :loading="index === 0 ? 'eager' : 'lazy'"
        />
      </div>

      <!-- Enhanced Dots Indicator -->
      <div class="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex items-center space-x-1.5 bg-black/20 backdrop-blur-sm rounded-full px-2 py-1">
        <button
          v-for="(_, index) in bannerImages"
          :key="index"
          @click="goToSlide(index)"
          class="relative transition-all duration-300 ease-out focus:outline-none focus:ring-1 focus:ring-white/30 rounded-full"
          :class="index === currentSlide
            ? 'w-4 h-2 bg-white/90 shadow-sm'
            : 'w-2 h-2 bg-white/30 hover:bg-white/50 hover:scale-105'"
          :aria-label="`الانتقال إلى الشريحة ${index + 1}`"
          :style="{
            borderRadius: index === currentSlide ? '8px' : '50%'
          }"
        >
          <div
            v-if="index === currentSlide"
            class="absolute inset-0 bg-gradient-to-r from-purple-300/60 to-pink-300/60 rounded-full opacity-70"
          />
        </button>
      </div>

      <!-- Loading indicator for smooth transitions -->
      <div
        v-if="isDragging"
        class="absolute inset-0 bg-black/10 pointer-events-none"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const bannerImages = [
  "/banner/1.png",
  "/banner/2.png",
  "/banner/3.png",
  "/banner/4.png",
]

const currentSlide = ref(0)
const isAutoPlaying = ref(true)
const isDragging = ref(false)
const startX = ref(0)
const currentX = ref(0)
const translateX = ref(0)
const dragProgress = ref(0) // -1 to 1, indicates drag direction and intensity
const sliderRef = ref<HTMLDivElement | null>(null)
const autoPlayRef = ref<NodeJS.Timeout | null>(null)
const containerRef = ref<HTMLDivElement | null>(null)

// Auto-slide functionality
const startAutoPlay = () => {
  if (autoPlayRef.value) clearInterval(autoPlayRef.value)
  autoPlayRef.value = setInterval(() => {
    currentSlide.value = (currentSlide.value + 1) % bannerImages.length
  }, 4000)
}

const stopAutoPlay = () => {
  if (autoPlayRef.value) {
    clearInterval(autoPlayRef.value)
    autoPlayRef.value = null
  }
}

watch(isAutoPlaying, (newVal) => {
  if (newVal) {
    startAutoPlay()
  } else {
    stopAutoPlay()
  }
})

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % bannerImages.length
}

const prevSlide = () => {
  currentSlide.value = (currentSlide.value - 1 + bannerImages.length) % bannerImages.length
}

const goToSlide = (index: number) => {
  currentSlide.value = index
}

// Touch and mouse event handlers
const handleStart = (clientX: number) => {
  isDragging.value = true
  startX.value = clientX
  currentX.value = clientX
  isAutoPlaying.value = false
}

const handleMove = (clientX: number) => {
  if (!isDragging.value || !containerRef.value) return

  currentX.value = clientX
  const diff = clientX - startX.value
  const containerWidth = containerRef.value.offsetWidth
  const maxDrag = containerWidth * 0.3 // Limit drag to 30% of container width

  // Clamp the translation
  const clampedDiff = Math.max(-maxDrag, Math.min(maxDrag, diff))
  translateX.value = clampedDiff

  // Calculate drag progress (-1 to 1)
  dragProgress.value = clampedDiff / maxDrag
}

const handleEnd = () => {
  if (!isDragging.value) return

  isDragging.value = false
  const diff = currentX.value - startX.value
  const threshold = 50 // Minimum distance to trigger slide change

  if (Math.abs(diff) > threshold) {
    if (diff > 0) {
      prevSlide()
    } else {
      nextSlide()
    }
  }

  // Reset states
  translateX.value = 0
  dragProgress.value = 0
  isAutoPlaying.value = true
}

// Mouse events
const handleMouseDown = (e: MouseEvent) => {
  e.preventDefault()
  handleStart(e.clientX)
}

const handleMouseMove = (e: MouseEvent) => {
  handleMove(e.clientX)
}

const handleMouseUp = () => {
  handleEnd()
}

const handleMouseLeave = () => {
  if (isDragging.value) {
    handleEnd()
  }
}

// Touch events
const handleTouchStart = (e: TouchEvent) => {
  e.preventDefault()
  handleStart(e.touches[0].clientX)
}

const handleTouchMove = (e: TouchEvent) => {
  e.preventDefault()
  handleMove(e.touches[0].clientX)
}

const handleTouchEnd = (e: TouchEvent) => {
  e.preventDefault()
  handleEnd()
}

// Pause auto-play on hover
const handleMouseEnter = () => {
  isAutoPlaying.value = false
}

const handleMouseLeaveContainer = () => {
  if (!isDragging.value) {
    isAutoPlaying.value = true
  }
}

// Image display logic
const getImageOpacity = (index: number) => {
  const isActive = index === currentSlide.value
  const isPrev = index === (currentSlide.value - 1 + bannerImages.length) % bannerImages.length
  const isNext = index === (currentSlide.value + 1) % bannerImages.length

  if (isActive) {
    return 1
  } else if (isDragging.value) {
    if (isPrev && dragProgress.value > 0) {
      return Math.min(0.7, dragProgress.value * 2)
    } else if (isNext && dragProgress.value < 0) {
      return Math.min(0.7, Math.abs(dragProgress.value) * 2)
    }
  }
  return 0
}

const getImageTransform = (index: number) => {
  const isActive = index === currentSlide.value
  const isPrev = index === (currentSlide.value - 1 + bannerImages.length) % bannerImages.length
  const isNext = index === (currentSlide.value + 1) % bannerImages.length

  if (isActive) {
    if (isDragging.value) {
      return `translateX(${translateX.value}px)`
    }
  } else if (isDragging.value) {
    if (isPrev && dragProgress.value > 0) {
      return `translateX(${translateX.value - (containerRef.value?.offsetWidth || 0)}px)`
    } else if (isNext && dragProgress.value < 0) {
      return `translateX(${translateX.value + (containerRef.value?.offsetWidth || 0)}px)`
    }
  }
  return 'translateX(0px)'
}

onMounted(() => {
  startAutoPlay()
})

onUnmounted(() => {
  stopAutoPlay()
})
</script>
