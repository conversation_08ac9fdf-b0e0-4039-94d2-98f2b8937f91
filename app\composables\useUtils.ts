/**
 * Utility composable for common formatting and helper functions
 */

export const useUtils = () => {
  /**
   * Format currency with proper symbol and decimals
   */
  const formatCurrency = (amount: number, currency: string = 'USD'): string => {
    const currencySymbols: Record<string, string> = {
      USD: '$',
      EUR: '€',
      BTC: '₿'
    };

    const symbol = currencySymbols[currency] || currency;
    
    if (currency === 'BTC') {
      return `${symbol}${amount.toFixed(8)}`;
    }
    
    return `${symbol}${amount.toFixed(2)}`;
  };

  /**
   * Format date in a human-readable format
   */
  const formatDate = (dateString: string | Date, options?: Intl.DateTimeFormatOptions): string => {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };

    return date.toLocaleDateString('en-US', { ...defaultOptions, ...options });
  };

  /**
   * Format date with time
   */
  const formatDateTime = (dateString: string | Date): string => {
    return formatDate(dateString, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  /**
   * Format relative time (e.g., "2 hours ago")
   */
  const formatRelativeTime = (dateString: string | Date): string => {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays !== 1 ? 's' : ''} ago`;
    }

    return formatDate(date);
  };

  /**
   * Truncate text to specified length
   */
  const truncateText = (text: string, maxLength: number = 100): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
  };

  /**
   * Generate user initials from name
   */
  const getUserInitials = (firstName: string, lastName: string): string => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  /**
   * Validate email format
   */
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  /**
   * Validate phone number format
   */
  const isValidPhone = (phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  };

  /**
   * Generate random string for IDs
   */
  const generateId = (length: number = 8): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  /**
   * Debounce function calls
   */
  const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  };

  /**
   * Deep clone an object
   */
  const deepClone = <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
    if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
    if (typeof obj === 'object') {
      const clonedObj = {} as T;
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
    return obj;
  };

  /**
   * Calculate percentage
   */
  const calculatePercentage = (value: number, total: number): number => {
    if (total === 0) return 0;
    return Math.round((value / total) * 100);
  };

  /**
   * Format file size
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * Capitalize first letter of each word
   */
  const capitalizeWords = (str: string): string => {
    return str.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  };

  /**
   * Convert string to slug format
   */
  const slugify = (str: string): string => {
    return str
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  /**
   * Check if value is empty (null, undefined, empty string, empty array, empty object)
   */
  const isEmpty = (value: any): boolean => {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim().length === 0;
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  };

  /**
   * Get status badge class based on status
   */
  const getStatusClass = (status: string): string => {
    const statusClasses: Record<string, string> = {
      // Order statuses
      pending: 'status-pending',
      processing: 'bg-blue-100 text-blue-800',
      completed: 'status-completed',
      cancelled: 'bg-red-100 text-red-800',
      
      // User statuses
      active: 'status-completed',
      inactive: 'bg-gray-100 text-gray-800',
      suspended: 'bg-red-100 text-red-800',
      
      // Transaction statuses
      success: 'status-completed',
      failed: 'bg-red-100 text-red-800',
      
      // Product statuses
      'in-stock': 'status-completed',
      'low-stock': 'status-pending',
      'out-of-stock': 'bg-red-100 text-red-800'
    };
    
    return statusClasses[status.toLowerCase()] || 'bg-gray-100 text-gray-800';
  };

  /**
   * Get role badge class based on role
   */
  const getRoleClass = (role: string): string => {
    const roleClasses: Record<string, string> = {
      admin: 'badge-admin',
      worker: 'badge-worker',
      distributor: 'badge-distributor',
      user: 'badge-user'
    };
    
    return roleClasses[role.toLowerCase()] || 'badge-user';
  };

  /**
   * Sort array by multiple criteria
   */
  const sortBy = <T>(array: T[], ...criteria: Array<keyof T | ((item: T) => any)>): T[] => {
    return [...array].sort((a, b) => {
      for (const criterion of criteria) {
        let aVal, bVal;
        
        if (typeof criterion === 'function') {
          aVal = criterion(a);
          bVal = criterion(b);
        } else {
          aVal = a[criterion];
          bVal = b[criterion];
        }
        
        if (aVal < bVal) return -1;
        if (aVal > bVal) return 1;
      }
      return 0;
    });
  };

  return {
    formatCurrency,
    formatDate,
    formatDateTime,
    formatRelativeTime,
    truncateText,
    getUserInitials,
    isValidEmail,
    isValidPhone,
    generateId,
    debounce,
    deepClone,
    calculatePercentage,
    formatFileSize,
    capitalizeWords,
    slugify,
    isEmpty,
    getStatusClass,
    getRoleClass,
    sortBy
  };
};
