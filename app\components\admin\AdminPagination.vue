<template>
  <div class="admin-pagination">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <!-- Items per page selector -->
      <div class="flex items-center gap-2">
        <span class="text-sm text-theme-secondary arabic-text">عرض</span>
        <select
          :value="itemsPerPage"
          @change="$emit('items-per-page-change', parseInt(($event.target as HTMLSelectElement).value))"
          class="pagination-select"
        >
          <option v-for="option in itemsPerPageOptions" :key="option" :value="option">
            {{ option }}
          </option>
        </select>
        <span class="text-sm text-theme-secondary arabic-text">من {{ totalItems }} عنصر</span>
      </div>

      <!-- Pagination controls -->
      <div class="flex items-center gap-2">
        <!-- First page -->
        <button
          @click="$emit('page-change', 1)"
          :disabled="currentPage === 1"
          class="pagination-button"
          title="الصفحة الأولى"
        >
          <svg class="w-4 h-4 rtl-flip" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
          </svg>
        </button>

        <!-- Previous page -->
        <button
          @click="$emit('page-change', currentPage - 1)"
          :disabled="currentPage === 1"
          class="pagination-button"
          title="الصفحة السابقة"
        >
          <svg class="w-4 h-4 rtl-flip" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <!-- Page numbers -->
        <div class="flex items-center gap-1">
          <template v-for="page in visiblePages" :key="page">
            <button
              v-if="page !== '...'"
              @click="$emit('page-change', page)"
              :class="[
                'pagination-number',
                page === currentPage ? 'pagination-number-active' : ''
              ]"
            >
              {{ page }}
            </button>
            <span v-else class="px-2 py-1 text-theme-muted">...</span>
          </template>
        </div>

        <!-- Next page -->
        <button
          @click="$emit('page-change', currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="pagination-button"
          title="الصفحة التالية"
        >
          <svg class="w-4 h-4 rtl-flip" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>

        <!-- Last page -->
        <button
          @click="$emit('page-change', totalPages)"
          :disabled="currentPage === totalPages"
          class="pagination-button"
          title="الصفحة الأخيرة"
        >
          <svg class="w-4 h-4 rtl-flip" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      <!-- Page info for mobile -->
      <div class="sm:hidden text-center">
        <span class="text-sm text-theme-secondary arabic-text">
          صفحة {{ currentPage }} من {{ totalPages }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  itemsPerPageOptions?: number[];
  maxVisiblePages?: number;
}

const props = withDefaults(defineProps<Props>(), {
  itemsPerPageOptions: () => [10, 20, 50, 100],
  maxVisiblePages: 7
});

const emit = defineEmits<{
  'page-change': [page: number];
  'items-per-page-change': [items: number];
}>();

const visiblePages = computed(() => {
  const pages: (number | string)[] = [];
  const { currentPage, totalPages, maxVisiblePages } = props;

  if (totalPages <= maxVisiblePages) {
    // Show all pages if total is less than max visible
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    // Always show first page
    pages.push(1);

    // Calculate start and end of visible range
    let start = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
    let end = Math.min(totalPages - 1, start + maxVisiblePages - 3);

    // Adjust start if end is at the limit
    if (end === totalPages - 1) {
      start = Math.max(2, end - maxVisiblePages + 3);
    }

    // Add ellipsis before start if needed
    if (start > 2) {
      pages.push('...');
    }

    // Add visible pages
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    // Add ellipsis after end if needed
    if (end < totalPages - 1) {
      pages.push('...');
    }

    // Always show last page
    if (totalPages > 1) {
      pages.push(totalPages);
    }
  }

  return pages;
});
</script>

<style scoped>
.admin-pagination {
  @apply p-4 bg-theme-surface border-t border-theme-border;
}

.pagination-select {
  @apply px-3 py-1 border border-theme-border bg-theme-surface text-theme-primary rounded focus:outline-none focus:ring-2 focus:ring-theme-primary focus:border-transparent;
}

.pagination-button {
  @apply p-2 rounded-lg border border-theme-border bg-theme-surface text-theme-primary hover:bg-theme-surface-light disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
}

.pagination-number {
  @apply px-3 py-1 rounded-lg border border-theme-border bg-theme-surface text-theme-primary hover:bg-theme-surface-light transition-colors duration-200;
}

.pagination-number-active {
  @apply bg-theme-primary text-white border-theme-primary hover:bg-theme-primary-hover;
}
</style>
