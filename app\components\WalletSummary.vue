<template>
  <div class="card">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-xl font-semibold text-gray-900">Wallet Balance</h2>
      <button 
        @click="refreshBalance"
        :disabled="isLoading"
        class="btn-secondary text-sm"
      >
        {{ isLoading ? 'Refreshing...' : 'Refresh' }}
      </button>
    </div>

    <!-- Balance Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <!-- USD Balance -->
      <div class="bg-blue-50 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-blue-600 font-medium">USD Balance</p>
            <p class="text-2xl font-bold text-blue-900">
              ${{ formatCurrency(balance.usd, 'usd') }}
            </p>
          </div>
          <div class="text-blue-600">
            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>

      <!-- EUR Balance -->
      <div class="bg-green-50 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-green-600 font-medium">EUR Balance</p>
            <p class="text-2xl font-bold text-green-900">
              €{{ formatCurrency(balance.eur, 'eur') }}
            </p>
          </div>
          <div class="text-green-600">
            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>

      <!-- BTC Balance -->
      <div class="bg-orange-50 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-orange-600 font-medium">BTC Balance</p>
            <p class="text-2xl font-bold text-orange-900">
              ₿{{ formatCurrency(balance.btc, 'btc') }}
            </p>
          </div>
          <div class="text-orange-600">
            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 5a1 1 0 000 2h1.586l-1.293 1.293a1 1 0 001.414 1.414L10 8.414l1.293 1.293a1 1 0 001.414-1.414L11.414 7H13a1 1 0 100-2H7zm3 6a1 1 0 011 1v2a1 1 0 11-2 0v-2a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Balance -->
    <div class="bg-gray-50 rounded-lg p-4 mb-6">
      <div class="text-center">
        <p class="text-sm text-gray-600 mb-1">Total Balance (USD Equivalent)</p>
        <p class="text-3xl font-bold text-gray-900">
          ${{ formatCurrency(totalBalanceUSD, 'usd') }}
        </p>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-2 gap-4">
      <button 
        @click="showDepositModal = true"
        class="btn-primary"
      >
        Deposit Funds
      </button>
      <NuxtLink to="/wallet" class="btn-secondary text-center">
        View Transactions
      </NuxtLink>
    </div>

    <!-- Deposit Modal -->
    <DepositModal 
      v-if="showDepositModal" 
      @close="showDepositModal = false"
      @success="handleDepositSuccess"
    />
  </div>
</template>

<script setup lang="ts">
const { balance, totalBalanceUSD, isLoading, fetchBalance } = useWalletStore();

const showDepositModal = ref(false);

const formatCurrency = (amount: number, currency: string) => {
  if (currency === 'btc') {
    return amount.toFixed(6);
  }
  return amount.toFixed(2);
};

const refreshBalance = async () => {
  await fetchBalance();
};

const handleDepositSuccess = () => {
  showDepositModal.value = false;
  refreshBalance();
};

// Fetch balance on component mount
onMounted(() => {
  fetchBalance();
});
</script>
