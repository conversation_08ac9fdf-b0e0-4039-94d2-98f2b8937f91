export default defineNuxtRouteMiddleware((to, from) => {
  const { isAuthenticated, canAccessAdmin } = useAuthStore();
  
  // First check if user is authenticated
  if (!isAuthenticated) {
    return navigateTo('/?login=required');
  }
  
  // Then check if user has admin access
  if (!canAccessAdmin) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Access denied. Admin privileges required.'
    });
  }
});
