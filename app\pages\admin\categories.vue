<template>
  <div>
    <!-- <PERSON>er -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Categories Management</h1>
        <p class="text-lg text-gray-600">Organize your product categories</p>
      </div>
      <button 
        @click="showCreateModal = true"
        class="btn-primary"
      >
        Add New Category
      </button>
    </div>

    <!-- Categories Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <div 
        v-for="category in categories" 
        :key="category.id"
        class="card hover:shadow-md transition-shadow"
      >
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div 
              class="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-lg"
              :style="{ backgroundColor: category.color || '#6B7280' }"
            >
              {{ category.name.charAt(0).toUpperCase() }}
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900">{{ category.name }}</h3>
              <p class="text-sm text-gray-600">{{ category.productCount }} products</p>
            </div>
          </div>
          
          <div class="flex items-center space-x-2">
            <span 
              :class="category.isActive ? 'status-completed' : 'bg-gray-100 text-gray-800'"
              class="badge text-xs"
            >
              {{ category.isActive ? 'Active' : 'Inactive' }}
            </span>
          </div>
        </div>

        <p class="text-gray-600 text-sm mb-4">{{ category.description }}</p>

        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            Created {{ formatDate(category.createdAt) }}
          </div>
          
          <div class="flex space-x-2">
            <button 
              @click="editCategory(category)"
              class="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Edit
            </button>
            <button 
              @click="toggleCategoryStatus(category)"
              class="text-yellow-600 hover:text-yellow-800 text-sm font-medium"
            >
              {{ category.isActive ? 'Deactivate' : 'Activate' }}
            </button>
            <button 
              @click="deleteCategory(category.id)"
              class="text-red-600 hover:text-red-800 text-sm font-medium"
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="categories.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No categories</h3>
      <p class="mt-1 text-sm text-gray-500">Get started by creating your first category.</p>
      <div class="mt-6">
        <button 
          @click="showCreateModal = true"
          class="btn-primary"
        >
          Add Category
        </button>
      </div>
    </div>

    <!-- Create/Edit Category Modal -->
    <CategoryModal 
      v-if="showCreateModal || showEditModal"
      :category="editingCategory"
      :is-editing="showEditModal"
      @close="closeModals"
      @success="handleCategorySuccess"
    />
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'dashboard',
  middleware: 'admin'
});

// SEO Meta
useHead({
  title: 'Categories Management - Admin Dashboard',
  meta: [
    { name: 'description', content: 'Manage product categories and organization.' }
  ]
});

const { formatDate } = useUtils();

// Mock categories data - TODO: backend - Replace with real categories API
const categories = reactive([
  {
    id: 1,
    name: 'Electronics',
    description: 'Electronic devices and gadgets',
    color: '#3B82F6',
    isActive: true,
    productCount: 45,
    createdAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 2,
    name: 'Home & Garden',
    description: 'Home improvement and garden supplies',
    color: '#10B981',
    isActive: true,
    productCount: 32,
    createdAt: '2024-01-20T14:30:00Z'
  },
  {
    id: 3,
    name: 'Sports',
    description: 'Sports equipment and accessories',
    color: '#F59E0B',
    isActive: true,
    productCount: 28,
    createdAt: '2024-02-01T09:15:00Z'
  },
  {
    id: 4,
    name: 'Books',
    description: 'Books and educational materials',
    color: '#8B5CF6',
    isActive: false,
    productCount: 15,
    createdAt: '2024-02-10T16:45:00Z'
  }
]);

// Modals
const showCreateModal = ref(false);
const showEditModal = ref(false);
const editingCategory = ref(null);

// Methods
const editCategory = (category: any) => {
  editingCategory.value = category;
  showEditModal.value = true;
};

const toggleCategoryStatus = async (category: any) => {
  const action = category.isActive ? 'deactivate' : 'activate';
  if (confirm(`Are you sure you want to ${action} this category?`)) {
    // TODO: backend - Implement category status toggle API
    category.isActive = !category.isActive;
    console.log(`${action} category:`, category.id);
  }
};

const deleteCategory = async (categoryId: number) => {
  const category = categories.find(c => c.id === categoryId);
  if (category && category.productCount > 0) {
    alert(`Cannot delete category with ${category.productCount} products. Please move or delete the products first.`);
    return;
  }

  if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
    // TODO: backend - Implement category deletion API
    const index = categories.findIndex(c => c.id === categoryId);
    if (index > -1) {
      categories.splice(index, 1);
    }
    console.log('Deleting category:', categoryId);
  }
};

const closeModals = () => {
  showCreateModal.value = false;
  showEditModal.value = false;
  editingCategory.value = null;
};

const handleCategorySuccess = async () => {
  closeModals();
  // TODO: backend - Refresh categories data
  console.log('Category saved successfully');
};
</script>
