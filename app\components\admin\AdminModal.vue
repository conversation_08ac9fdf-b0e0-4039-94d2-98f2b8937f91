<template>
  <Teleport to="body">
    <Transition name="modal" appear>
      <div v-if="modelValue" class="modal-overlay" @click="handleOverlayClick">
        <div class="modal-container" :class="sizeClasses" @click.stop>
          <!-- Header -->
          <div class="modal-header">
            <h3 class="modal-title arabic-text">{{ title }}</h3>
            <button
              @click="$emit('update:modelValue', false)"
              class="modal-close-button"
              :disabled="isLoading"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Content -->
          <div class="modal-content">
            <slot />
          </div>

          <!-- Footer -->
          <div v-if="$slots.footer || showDefaultFooter" class="modal-footer">
            <slot name="footer">
              <div class="flex justify-end gap-3">
                <button
                  @click="$emit('update:modelValue', false)"
                  :disabled="isLoading"
                  class="btn-secondary"
                >
                  {{ cancelLabel }}
                </button>
                <button
                  v-if="confirmLabel"
                  @click="$emit('confirm')"
                  :disabled="isLoading"
                  :class="[
                    'btn-primary',
                    confirmVariant === 'danger' ? 'btn-danger' : ''
                  ]"
                >
                  <div v-if="isLoading" class="flex items-center gap-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>جاري المعالجة...</span>
                  </div>
                  <span v-else>{{ confirmLabel }}</span>
                </button>
              </div>
            </slot>
          </div>

          <!-- Loading overlay -->
          <div v-if="isLoading && showLoadingOverlay" class="modal-loading">
            <div class="flex flex-col items-center gap-3">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-theme-primary"></div>
              <span class="text-theme-secondary arabic-text">{{ loadingMessage }}</span>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean;
  title: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closable?: boolean;
  showDefaultFooter?: boolean;
  confirmLabel?: string;
  cancelLabel?: string;
  confirmVariant?: 'primary' | 'danger';
  isLoading?: boolean;
  showLoadingOverlay?: boolean;
  loadingMessage?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closable: true,
  showDefaultFooter: true,
  cancelLabel: 'إلغاء',
  confirmVariant: 'primary',
  isLoading: false,
  showLoadingOverlay: false,
  loadingMessage: 'جاري المعالجة...'
});

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  confirm: [];
  close: [];
}>();

const sizeClasses = computed(() => {
  const sizes = {
    sm: 'modal-sm',
    md: 'modal-md',
    lg: 'modal-lg',
    xl: 'modal-xl',
    full: 'modal-full'
  };
  return sizes[props.size];
});

const handleOverlayClick = () => {
  if (props.closable && !props.isLoading) {
    emit('update:modelValue', false);
    emit('close');
  }
};

// Handle escape key
onMounted(() => {
  const handleEscape = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && props.modelValue && props.closable && !props.isLoading) {
      emit('update:modelValue', false);
      emit('close');
    }
  };
  
  document.addEventListener('keydown', handleEscape);
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleEscape);
  });
});

// Prevent body scroll when modal is open
watch(() => props.modelValue, (isOpen) => {
  if (process.client) {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }
});

onUnmounted(() => {
  if (process.client) {
    document.body.style.overflow = '';
  }
});
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
  backdrop-filter: blur(4px);
}

.modal-container {
  @apply bg-theme-surface rounded-xl shadow-2xl border border-theme-border max-h-[90vh] flex flex-col relative;
}

.modal-sm {
  @apply w-full max-w-sm;
}

.modal-md {
  @apply w-full max-w-md;
}

.modal-lg {
  @apply w-full max-w-2xl;
}

.modal-xl {
  @apply w-full max-w-4xl;
}

.modal-full {
  @apply w-full h-full max-w-none max-h-none rounded-none;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-theme-border;
}

.modal-title {
  @apply text-lg font-semibold text-theme-primary;
}

.modal-close-button {
  @apply p-2 rounded-lg text-theme-muted hover:text-theme-primary hover:bg-theme-surface-light transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.modal-content {
  @apply flex-1 p-6 overflow-y-auto;
}

.modal-footer {
  @apply p-6 border-t border-theme-border;
}

.modal-loading {
  @apply absolute inset-0 bg-theme-surface bg-opacity-90 flex items-center justify-center;
}

.btn-primary {
  @apply bg-theme-primary hover:bg-theme-primary-hover text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-theme-surface-light hover:bg-theme-surface text-theme-primary font-medium py-2 px-4 rounded-lg border border-theme-border transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white;
}

/* Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* Mobile responsive */
@media (max-width: 640px) {
  .modal-overlay {
    @apply p-2;
  }
  
  .modal-container {
    @apply max-h-[95vh];
  }
  
  .modal-header,
  .modal-content,
  .modal-footer {
    @apply p-4;
  }
  
  .modal-sm,
  .modal-md,
  .modal-lg,
  .modal-xl {
    @apply w-full max-w-none;
  }
}
</style>
