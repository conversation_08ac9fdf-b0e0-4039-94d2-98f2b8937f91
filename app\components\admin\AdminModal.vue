<template>
  <Teleport to="body">
    <Transition name="modal" appear>
      <div v-if="modelValue" class="modal-overlay" @click="handleOverlayClick">
        <div class="modal-container" :class="sizeClasses" @click.stop>
          <!-- Header -->
          <div class="modal-header">
            <h3 class="modal-title arabic-text">{{ title }}</h3>
            <button
              @click="$emit('update:modelValue', false)"
              class="modal-close-button"
              :disabled="isLoading"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Content -->
          <div class="modal-content">
            <slot />
          </div>

          <!-- Footer -->
          <div v-if="$slots.footer || showDefaultFooter" class="modal-footer">
            <slot name="footer">
              <div class="flex justify-end gap-3">
                <button
                  @click="$emit('update:modelValue', false)"
                  :disabled="isLoading"
                  class="btn-secondary"
                >
                  {{ cancelLabel }}
                </button>
                <button
                  v-if="confirmLabel"
                  @click="$emit('confirm')"
                  :disabled="isLoading"
                  :class="[
                    'btn-primary',
                    confirmVariant === 'danger' ? 'btn-danger' : ''
                  ]"
                >
                  <div v-if="isLoading" class="flex items-center gap-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>جاري المعالجة...</span>
                  </div>
                  <span v-else>{{ confirmLabel }}</span>
                </button>
              </div>
            </slot>
          </div>

          <!-- Loading overlay -->
          <div v-if="isLoading && showLoadingOverlay" class="modal-loading">
            <div class="flex flex-col items-center gap-3">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-theme-primary"></div>
              <span class="text-theme-secondary arabic-text">{{ loadingMessage }}</span>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean;
  title: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closable?: boolean;
  showDefaultFooter?: boolean;
  confirmLabel?: string;
  cancelLabel?: string;
  confirmVariant?: 'primary' | 'danger';
  isLoading?: boolean;
  showLoadingOverlay?: boolean;
  loadingMessage?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closable: true,
  showDefaultFooter: true,
  cancelLabel: 'إلغاء',
  confirmVariant: 'primary',
  isLoading: false,
  showLoadingOverlay: false,
  loadingMessage: 'جاري المعالجة...'
});

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  confirm: [];
  close: [];
}>();

const sizeClasses = computed(() => {
  const sizes = {
    sm: 'modal-sm',
    md: 'modal-md',
    lg: 'modal-lg',
    xl: 'modal-xl',
    full: 'modal-full'
  };
  return sizes[props.size];
});

const handleOverlayClick = () => {
  if (props.closable && !props.isLoading) {
    emit('update:modelValue', false);
    emit('close');
  }
};

// Handle escape key
onMounted(() => {
  const handleEscape = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && props.modelValue && props.closable && !props.isLoading) {
      emit('update:modelValue', false);
      emit('close');
    }
  };
  
  document.addEventListener('keydown', handleEscape);
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleEscape);
  });
});

// Prevent body scroll when modal is open
watch(() => props.modelValue, (isOpen) => {
  if (process.client) {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }
});

onUnmounted(() => {
  if (process.client) {
    document.body.style.overflow = '';
  }
});
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 flex items-center justify-center p-4 z-50;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(12px);
  animation: fadeIn 0.3s ease-out;
}

.modal-container {
  @apply max-h-[90vh] flex flex-col relative;
  background: var(--glass-bg);
  backdrop-filter: blur(24px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-xl);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-sm {
  @apply w-full max-w-sm;
}

.modal-md {
  @apply w-full max-w-md;
}

.modal-lg {
  @apply w-full max-w-2xl;
}

.modal-xl {
  @apply w-full max-w-4xl;
}

.modal-full {
  @apply w-full h-full max-w-none max-h-none rounded-none;
}

.modal-header {
  @apply flex items-center justify-between p-6;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1));
  border-bottom: 1px solid var(--glass-border);
  border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

.modal-title {
  @apply text-xl font-bold;
  color: rgb(var(--color-text-primary));
  font-family: 'Cairo', sans-serif;
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-close-button {
  @apply p-3 rounded-xl transition-all duration-300;
  background: rgba(239, 68, 68, 0.1);
  color: rgb(var(--color-error));
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.modal-close-button:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.2);
}

.modal-close-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.modal-content {
  @apply flex-1 p-6 overflow-y-auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(147, 51, 234, 0.3) transparent;
}

.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track {
  background: transparent;
}

.modal-content::-webkit-scrollbar-thumb {
  background: rgba(147, 51, 234, 0.3);
  border-radius: 3px;
}

.modal-footer {
  @apply p-6;
  border-top: 1px solid var(--glass-border);
  background: rgba(31, 41, 55, 0.3);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
}

.modal-loading {
  @apply absolute inset-0 flex items-center justify-center;
  background: var(--glass-bg);
  backdrop-filter: blur(12px);
  border-radius: var(--border-radius-xl);
}

.btn-primary {
  @apply font-semibold py-3 px-6 rounded-xl transition-all duration-300;
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  color: white;
  border: none;
  box-shadow: 0 4px 16px rgba(var(--color-primary), 0.3);
  font-family: 'Cairo', sans-serif;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--color-primary), 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  @apply font-semibold py-3 px-6 rounded-xl transition-all duration-300;
  background: rgba(147, 51, 234, 0.1);
  color: rgb(var(--color-primary));
  border: 1px solid var(--glass-border);
  font-family: 'Cairo', sans-serif;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(147, 51, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--color-primary), 0.2);
  border-color: rgb(var(--color-primary));
}

.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-danger {
  background: linear-gradient(135deg, rgb(var(--color-error)), rgb(220, 38, 38));
  color: white;
  box-shadow: 0 4px 16px rgba(var(--color-error), 0.3);
}

.btn-danger:hover {
  box-shadow: 0 8px 24px rgba(var(--color-error), 0.4);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Transitions */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.8) translateY(-50px);
}

/* Mobile responsive */
@media (max-width: 640px) {
  .modal-overlay {
    @apply p-3;
  }

  .modal-container {
    @apply max-h-[95vh];
    animation: modalSlideInMobile 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .modal-header,
  .modal-content,
  .modal-footer {
    @apply p-4;
  }

  .modal-title {
    @apply text-lg;
  }

  .btn-primary,
  .btn-secondary {
    @apply py-2 px-4 text-sm;
  }

  .modal-sm,
  .modal-md,
  .modal-lg,
  .modal-xl {
    @apply w-full max-w-none;
  }
}

@keyframes modalSlideInMobile {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* RTL Support */
[dir="rtl"] .modal-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .modal-footer .flex {
  flex-direction: row-reverse;
}

/* Loading Animation */
.modal-loading .animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Accessibility */
.modal-container:focus {
  outline: 2px solid rgb(var(--color-primary));
  outline-offset: 2px;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .modal-container,
  .modal-overlay {
    animation: none;
  }

  .modal-enter-active,
  .modal-leave-active {
    transition: opacity 0.2s ease;
  }

  .modal-enter-from,
  .modal-leave-to {
    transform: none;
  }
}
</style>
