<template>
  <div class="min-h-screen bg-theme-background" dir="rtl">
    <div class="container mx-auto px-4 py-6">

      <!-- Products Grid - 3 columns on mobile, exactly like Bentakon -->
      <div class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 md:gap-4 mb-8">
        <ProductCard
          v-for="product in paginatedProducts"
          :key="product.id"
          :product="product"
        />
      </div>

      <!-- Pagination - Always show for testing -->
      <Pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-change="handlePageChange"
        class="mt-8"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { mockProducts } from '~/data/mockData'

// SEO Meta
useSeoMeta({
  title: 'بنتاكون - متجر الألعاب الرقمية',
  description: 'تصفح جميع منتجاتنا من الألعاب الرقمية وبطاقات الهدايا',
  ogTitle: 'بنتاكون - متجر الألعاب الرقمية',
  ogDescription: 'تصفح جميع منتجاتنا من الألعاب الرقمية وبطاقات الهدايا',
})

// Pagination
const currentPage = ref(1)
const productsPerPage = 20

// Sort products by popularity (like Bentakon)
const sortedProducts = computed(() => {
  return [...mockProducts].sort((a, b) => b.commentCount - a.commentCount)
})

// Pagination logic
const { paginatedProducts, totalPages } = computed(() => {
  const startIndex = (currentPage.value - 1) * productsPerPage
  const endIndex = startIndex + productsPerPage
  const paginatedProducts = sortedProducts.value.slice(startIndex, endIndex)
  const totalPages = Math.ceil(sortedProducts.value.length / productsPerPage)

  return { paginatedProducts, totalPages }
}).value

const handlePageChange = (page: number) => {
  currentPage.value = page
  // Scroll to top when page changes
  window.scrollTo({ top: 0, behavior: 'smooth' })
}
</script>
