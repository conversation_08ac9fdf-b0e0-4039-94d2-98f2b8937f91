@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-900 text-white;
  }

  /* RTL Support */
  [dir="rtl"] {
    direction: rtl;
  }

  /* Arabic font support */
  .arabic-text {
    font-family: 'Noto Sans Arabic', 'Inter', system-ui, sans-serif;
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-gray-800 rounded-lg shadow-sm border border-gray-700 p-6;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-600 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent;
  }

  /* Gaming-style product cards */
  .product-card {
    @apply bg-gray-800 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300 border border-gray-700;
  }

  .product-card:hover {
    @apply border-purple-500 shadow-lg shadow-purple-500/20;
  }

  /* Gradient backgrounds */
  .gradient-purple {
    background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);
  }

  .gradient-blue {
    background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);
  }

  .gradient-green {
    background: linear-gradient(135deg, #10B981 0%, #3B82F6 100%);
  }

  /* Text utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Aspect ratio utilities */
  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  /* Mobile responsive grid improvements */
  @media (max-width: 768px) {
    .grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-admin {
    @apply badge bg-purple-100 text-purple-800;
  }
  
  .badge-worker {
    @apply badge bg-blue-100 text-blue-800;
  }
  
  .badge-distributor {
    @apply badge bg-green-100 text-green-800;
  }
  
  .badge-user {
    @apply badge bg-gray-100 text-gray-800;
  }
  
  .status-pending {
    @apply badge bg-yellow-100 text-yellow-800;
  }
  
  .status-completed {
    @apply badge bg-green-100 text-green-800;
  }
  
  .status-declined {
    @apply badge bg-red-100 text-red-800;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #CBD5E0 #F7FAFC;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #F7FAFC;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #CBD5E0;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #A0AEC0;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.5s ease-out;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive improvements */
@media (max-width: 640px) {
  .card {
    @apply p-4;
  }

  .btn-primary,
  .btn-secondary,
  .btn-danger {
    @apply text-sm px-3 py-2;
  }
}
