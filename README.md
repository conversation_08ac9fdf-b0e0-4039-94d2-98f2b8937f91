# Nuxt.js Application

A modern Vue.js application built with Nuxt 4, featuring server-side rendering, automatic routing, and development tools.

## 🚀 Project Overview

This is a Nuxt.js application using the latest version (4.0.2) with Vue 3.5.18 and Vue Router 4.5.1. The project follows <PERSON>ux<PERSON>'s convention-based structure for optimal development experience and performance.

## 📁 Project Structure

```
tenant/
├── 📁 app/                     # Application source code
│   └── app.vue                 # Root Vue component
├── 📁 public/                  # Static assets served directly
│   ├── favicon.ico             # Website favicon
│   └── robots.txt              # Search engine crawling rules
├── 📁 node_modules/            # Dependencies (auto-generated)
├── 📁 .nuxt/                   # Nuxt build cache (auto-generated)
├── 📁 .git/                    # Git version control
├── 📄 package.json             # Project dependencies and scripts
├── 📄 package-lock.json        # Locked dependency versions
├── 📄 nuxt.config.ts           # Nuxt configuration
├── 📄 tsconfig.json            # TypeScript configuration
├── 📄 .gitignore               # Git ignore rules
└── 📄 README.md                # Project documentation
```

### 🗂️ Directory Breakdown

#### `/app/` - Application Core
- **`app.vue`** - Root component that wraps all pages
  - Contains `<NuxtRouteAnnouncer />` for accessibility
  - Displays `<NuxtWelcome />` component (default Nuxt welcome page)

#### `/public/` - Static Assets
- **`favicon.ico`** - Browser tab icon
- **`robots.txt`** - SEO and crawler directives

#### Auto-Generated Directories
- **`.nuxt/`** - Nuxt's build cache and generated files
- **`node_modules/`** - NPM dependencies
- **`.output/`** - Production build output (created during build)
- **`.nitro/`** - Nitro server build cache
- **`.cache/`** - Various build caches
- **`dist/`** - Distribution files

## 🛠️ Configuration Files

### `package.json`
```json
{
  "name": "nuxt-app",
  "private": true,
  "type": "module",
  "scripts": {
    "build": "nuxt build",
    "dev": "nuxt dev",
    "generate": "nuxt generate",
    "preview": "nuxt preview",
    "postinstall": "nuxt prepare"
  },
  "dependencies": {
    "nuxt": "^4.0.1",
    "vue": "^3.5.18",
    "vue-router": "^4.5.1"
  }
}
```

### `nuxt.config.ts`
```typescript
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true }
})
```

### `tsconfig.json`
- Extends Nuxt's TypeScript configuration
- References auto-generated TypeScript configs in `.nuxt/`

## 🎯 Key Features

- **Nuxt 4.0** - Latest version with improved performance
- **Vue 3.5** - Composition API and modern Vue features
- **TypeScript Support** - Full TypeScript integration
- **Development Tools** - Nuxt DevTools enabled
- **Auto-routing** - File-based routing system
- **SSR/SSG Ready** - Server-side rendering and static generation
- **Hot Module Replacement** - Fast development experience

## 📋 Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server |
| `npm run build` | Build for production |
| `npm run generate` | Generate static site |
| `npm run preview` | Preview production build |
| `npm run postinstall` | Prepare Nuxt (auto-run after install) |

## 🚦 Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   Navigate to `http://localhost:3000`

## 🏗️ Development Structure

### Potential Directories (Convention-based)
When you start developing, you might add these conventional Nuxt directories:

```
├── 📁 pages/                   # Auto-routed pages
├── 📁 components/              # Vue components
├── 📁 layouts/                 # Page layouts
├── 📁 middleware/              # Route middleware
├── 📁 plugins/                 # Vue plugins
├── 📁 composables/             # Vue composables
├── 📁 utils/                   # Utility functions
├── 📁 stores/                  # State management (Pinia)
├── 📁 server/                  # Server-side code
│   ├── api/                    # API routes
│   └── middleware/             # Server middleware
├── 📁 assets/                  # Build-time assets
└── 📁 content/                 # Content files (if using @nuxt/content)
```

## 🔧 Technology Stack

- **Framework:** Nuxt.js 4.0.2
- **Frontend:** Vue.js 3.5.18
- **Routing:** Vue Router 4.5.1
- **Language:** TypeScript
- **Build Tool:** Vite (via Nuxt)
- **Package Manager:** NPM

## 📝 Notes

- This is a minimal Nuxt application setup
- The project uses ES modules (`"type": "module"`)
- DevTools are enabled for development
- The app currently shows the default Nuxt welcome page
- Ready for expansion with pages, components, and features

## 📋 Changelog

### ✅ Authentication System Implementation
- **✅ Updated** `app/components/AuthModal.vue` - Modern authentication component with login/signup functionality
- **✅ Updated** `app/stores/auth.ts` - Development-friendly auth store with localStorage persistence
- **✅ Updated** `app/middleware/auth.ts` - Route protection middleware with auth state initialization
- **✅ Updated** `app/layouts/default.vue` - Integrated auth modal and user menu with logout functionality
- **✅ Updated** `app/pages/profile/index.vue` - Connected to auth store for user data display
- **✅ Updated** `app/assets/css/main.css` - Enhanced with animation classes for auth modal

### ✅ New Pages Created
- **➕ Added** `app/pages/wallet/index.vue` - Complete wallet management with glassmorphism design
- **➕ Added** `app/pages/profile/index.vue` - User profile management with security settings
- **➕ Added** `app/pages/orders/index.vue` - Order history and tracking system
- **✅ Updated** `app/components/DepositModal.vue` - Arabic translation and glassmorphism styling
- **✅ Updated** `app/components/WithdrawModal.vue` - Header styling improvements

### 🎨 Design Enhancements
- **✅ Removed** White line under header for cleaner design
- **✅ Enhanced** Glassmorphism effects with 20px backdrop blur
- **✅ Improved** Cairo font integration across all new pages
- **✅ Optimized** Mobile navigation with centered logo and enhanced glassmorphism

### ✅ Wallet Page Enhancement with Advanced Features
- **✅ Updated** `app/pages/wallet/index.vue` - Complete redesign with new features and composable integration
- **➕ Added** `app/composables/useWallet.ts` - Comprehensive wallet management composable with filtering, pagination, currency conversion, and CSV export
- **➕ Added** `app/composables/useExchangeRates.ts` - Exchange rate management for SAR/USD conversion
- **➕ Added** `app/backend/api/wallet.ts` - Backend API stubs for wallet operations with mock data

## 🔗 Useful Links

- [Nuxt.js Documentation](https://nuxt.com/)
- [Vue.js Documentation](https://vuejs.org/)
- [TypeScript Documentation](https://www.typescriptlang.org/)