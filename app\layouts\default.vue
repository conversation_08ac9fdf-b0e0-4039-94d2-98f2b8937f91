<template>
  <div class="min-h-screen bg-theme-background">
    <!-- Header -->
    <header
      class="bg-theme-glass backdrop-blur-md sticky top-0 z-30"
      role="banner"
    >
      <!-- Desktop Header -->
      <div class="hidden lg:block">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex items-center justify-between h-16">
            <!-- Logo -->
            <NuxtLink
              to="/"
              class="flex items-center space-x-3 space-x-reverse group"
              aria-label="بنتاكون - الصفحة الرئيسية"
            >
              <div class="w-8 h-8 rounded-lg overflow-hidden shadow-md group-hover:shadow-lg transition-shadow duration-200">
                <img
                  src="/logo.jpg"
                  alt="بنتاكون"
                  class="w-full h-full object-cover"
                />
              </div>
              <span class="text-xl font-bold text-white-force">بنتاكون</span>
            </NuxtLink>

            <!-- Navigation Links -->
            <nav class="hidden xl:flex items-center space-x-8 space-x-reverse">
              <NuxtLink
                to="/"
                class="flex items-center space-x-2 space-x-reverse text-theme-secondary hover:text-white-force transition-colors duration-200 group"
              >
                <span class="text-sm font-medium">الرئيسية</span>
              </NuxtLink>
              <NuxtLink
                to="/shop"
                class="flex items-center space-x-2 space-x-reverse text-theme-secondary hover:text-white-force transition-colors duration-200 group"
              >
                <span class="text-sm font-medium">المتجر</span>
              </NuxtLink>
              <NuxtLink
                to="/wallet"
                class="flex items-center space-x-2 space-x-reverse text-theme-secondary hover:text-white-force transition-colors duration-200 group"
              >
                <span class="text-sm font-medium">المحفظة</span>
              </NuxtLink>
              <NuxtLink
                to="/profile"
                class="flex items-center space-x-2 space-x-reverse text-theme-secondary hover:text-white-force transition-colors duration-200 group"
              >
                <span class="text-sm font-medium">الملف الشخصي</span>
              </NuxtLink>
            </nav>

            <!-- Search Bar - Center -->
            <div class="flex-1 max-w-md mx-8">
              <div class="relative w-full">
                <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <input
                  type="text"
                  placeholder="ابحث عن الألعاب أو المنتجات"
                  v-model="searchQuery"
                  class="input-field pl-10 pr-4 py-2 text-sm"
                />
              </div>
            </div>

            <!-- Right Side - User Area -->
            <div class="flex items-center">
              <div v-if="isAuthenticated && user" class="relative user-menu-container">
                <button
                  @click="showUserMenu = !showUserMenu"
                  class="flex items-center space-x-3 space-x-reverse p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200"
                >
                  <div class="w-8 h-8 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                  </div>
                  <div class="text-right">
                    <p class="text-sm font-medium text-white-force">{{ user.name }}</p>
                    <p class="text-xs text-theme-muted">{{ user.walletBalance || 0 }} ر.س</p>
                  </div>
                  <svg class="w-4 h-4 text-gray-400 transition-transform duration-200" :class="{ 'rotate-180': showUserMenu }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>

                <!-- User Dropdown Menu -->
                <div v-if="showUserMenu" class="absolute left-0 top-full mt-2 w-48 bg-gray-800/95 backdrop-blur-md border border-gray-700/50 rounded-lg shadow-xl z-50 animate-slide-down">
                  <div class="p-2">
                    <NuxtLink
                      to="/profile"
                      class="flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-lg hover:bg-gray-700/50 transition-colors duration-200 text-gray-200 hover:text-white"
                      @click="showUserMenu = false"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                      </svg>
                      <span class="text-sm">الملف الشخصي</span>
                    </NuxtLink>
                    <NuxtLink
                      to="/wallet"
                      class="flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-lg hover:bg-gray-700/50 transition-colors duration-200 text-gray-200 hover:text-white"
                      @click="showUserMenu = false"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                      </svg>
                      <span class="text-sm">المحفظة</span>
                    </NuxtLink>
                    <div v-if="user && user.role === 'admin'">
                      <NuxtLink
                        to="/admin"
                        class="flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-lg hover:bg-gray-700/50 transition-colors duration-200 text-gray-200 hover:text-white"
                        @click="showUserMenu = false"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="text-sm">لوحة التحكم</span>
                      </NuxtLink>
                    </div>
                    <hr class="my-2 border-gray-700/50" />
                    <button
                      @click="handleLogout"
                      class="flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-lg hover:bg-red-500/10 transition-colors duration-200 text-gray-200 hover:text-red-400 w-full text-right"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                      </svg>
                      <span class="text-sm">تسجيل الخروج</span>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Login Button for Guests -->
              <button
                v-else
                @click="showAuthModal = true"
                class="btn-primary px-4 py-2 text-sm"
              >
                تسجيل الدخول
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- Mobile Header -->
      <div class="lg:hidden">
        <div class="px-4 py-2 space-y-2">
          <!-- Top Row: Centered Logo with User Area -->
          <div class="relative flex items-center">
            <!-- Centered Logo -->
            <div class="mobile-header-logo">
              <NuxtLink
                to="/"
                class="flex items-center space-x-2 space-x-reverse"
                aria-label="بنتاكون - الصفحة الرئيسية"
              >
                <div class="w-8 h-8 rounded-lg overflow-hidden shadow-md">
                  <img
                    src="/logo.jpg"
                    alt="بنتاكون"
                    class="w-full h-full object-cover"
                  />
                </div>
                <span class="text-lg font-bold text-white-force">بنتاكون</span>
              </NuxtLink>
            </div>

            <!-- User Area - Mobile (Positioned Absolutely) -->
            <div class="mobile-header-user">
              <div v-if="user">
                <NuxtLink
                  to="/profile"
                  class="flex items-center space-x-2 space-x-reverse bg-theme-glass hover:bg-theme-glass-hover text-white-force py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200"
                >
                  <div class="w-6 h-6 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                  </div>
                  <span>{{ user.name }}</span>
                </NuxtLink>
              </div>
              <button
                v-else
                @click="showAuthModal = true"
                class="btn-primary py-2 px-4 text-sm"
              >
                تسجيل الدخول
              </button>
            </div>
          </div>

          <!-- Second Row: Menu Button and Search Bar -->
          <div class="flex items-center space-x-3 space-x-reverse">
            <!-- Menu Button -->
            <button
              class="flex items-center space-x-2 space-x-reverse p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 flex-shrink-0"
              @click="showMobileMenu = !showMobileMenu"
              :aria-label="showMobileMenu ? 'إغلاق القائمة' : 'فتح القائمة'"
            >
              <svg class="w-5 h-5 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
            </button>

            <!-- Search Bar -->
            <div class="relative flex-1">
              <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              <input
                type="text"
                placeholder="ابحث عن الألعاب أو المنتجات"
                v-model="searchQuery"
                class="input-field pl-10 pr-4 py-2 text-sm"
              />
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Mobile Sidebar (when menu is open) -->
    <div v-if="showMobileMenu" class="lg:hidden fixed inset-0 z-40 mobile-nav-backdrop" @click="showMobileMenu = false">
      <div class="fixed left-0 top-0 h-full w-64 mobile-nav-drawer p-4" @click.stop>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-lg font-bold text-white-force">القائمة</span>
            <button @click="showMobileMenu = false" class="text-theme-muted hover:text-white-force transition-colors duration-200">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <div class="space-y-2">
            <NuxtLink
              to="/"
              class="mobile-nav-item block px-3 py-2"
              @click="showMobileMenu = false"
            >
              الرئيسية
            </NuxtLink>
            <NuxtLink
              to="/shop"
              class="mobile-nav-item block px-3 py-2"
              @click="showMobileMenu = false"
            >
              المتجر
            </NuxtLink>
            <NuxtLink
              to="/wallet"
              class="mobile-nav-item block px-3 py-2"
              @click="showMobileMenu = false"
            >
              المحفظة
            </NuxtLink>
            <NuxtLink
              to="/profile"
              class="mobile-nav-item block px-3 py-2"
              @click="showMobileMenu = false"
            >
              الملف الشخصي
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <main>
      <slot />
    </main>

    <!-- Auth Modal -->
    <AuthModal
      v-if="showAuthModal"
      @close="showAuthModal = false"
      @success="showAuthModal = false"
    />
  </div>
</template>

<script setup lang="ts">
// Auth store
const { user, isAuthenticated, logout, initializeAuth } = useAuthStore()

// Reactive state
const searchQuery = ref('')
const showUserMenu = ref(false)
const showMobileMenu = ref(false)
const showAuthModal = ref(false)

// Initialize auth on mount
onMounted(() => {
  initializeAuth()
})

// Methods
const handleLogout = async () => {
  await logout()
  showUserMenu.value = false
}

// Check for login requirement from URL
const route = useRoute()
if (route.query.login === 'required') {
  showAuthModal.value = true
}
</script>
