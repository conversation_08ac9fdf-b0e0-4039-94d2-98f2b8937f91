import { defineStore } from 'pinia';

export interface User {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'worker' | 'distributor' | 'user';
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: false
  }),

  getters: {
    isAdmin: (state) => state.user?.role === 'admin',
    isWorker: (state) => state.user?.role === 'worker',
    isDistributor: (state) => state.user?.role === 'distributor',
    isUser: (state) => state.user?.role === 'user',
    
    canAccessAdmin: (state) => state.user?.role === 'admin',
    canManageOrders: (state) => ['admin', 'worker'].includes(state.user?.role || ''),
    canViewDashboard: (state) => ['admin', 'worker', 'distributor'].includes(state.user?.role || ''),
    
    userDisplayName: (state) => state.user?.name || 'Guest',
    userRole: (state) => state.user?.role || 'guest'
  },

  actions: {
    async login(email: string, password: string, rememberMe: boolean = false) {
      this.isLoading = true;

      try {
        // Development mode - accept any credentials
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay

        // Create user object from provided email
        const user = {
          id: Date.now(),
          name: email.split('@')[0], // Use email prefix as name
          email: email,
          role: 'user' as const
        };

        const token = 'dev_token_' + Date.now();

        this.user = user;
        this.token = token;
        this.isAuthenticated = true;

        // Store in localStorage for persistence
        if (process.client) {
          localStorage.setItem('auth_token', token);
          localStorage.setItem('auth_user', JSON.stringify(user));
          if (rememberMe) {
            localStorage.setItem('auth_remember', 'true');
          }
          console.log('Login successful, stored in localStorage:', { user: user.name, email: user.email });
        }

        return { success: true };
      } catch (error: any) {
        console.error('Login error:', error);
        throw new Error('فشل في تسجيل الدخول');
      } finally {
        this.isLoading = false;
      }
    },

    async register(userData: { name: string; email: string; password: string; phone?: string }) {
      this.isLoading = true;

      try {
        // Development mode - accept any registration
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay

        const user = {
          id: Date.now(),
          name: userData.name,
          email: userData.email,
          role: 'user' as const
        };

        const token = 'dev_token_' + Date.now();

        this.user = user;
        this.token = token;
        this.isAuthenticated = true;

        // Store in localStorage for persistence
        if (process.client) {
          localStorage.setItem('auth_token', token);
          localStorage.setItem('auth_user', JSON.stringify(user));
        }

        return { success: true };
      } catch (error: any) {
        console.error('Registration error:', error);
        throw new Error('فشل في إنشاء الحساب');
      } finally {
        this.isLoading = false;
      }
    },

    async logout() {
      this.user = null;
      this.token = null;
      this.isAuthenticated = false;
      
      // Clear localStorage
      if (process.client) {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
      }
      
      // Redirect to home
      await navigateTo('/');
    },

    async initializeAuth() {
      // Check for stored authentication on app start
      if (process.client) {
        try {
          const token = localStorage.getItem('auth_token');
          const userStr = localStorage.getItem('auth_user');

          if (token && userStr) {
            const user = JSON.parse(userStr);
            this.token = token;
            this.user = user;
            this.isAuthenticated = true;
            console.log('Auth state restored:', { user: user.name, email: user.email });
          } else {
            console.log('No stored auth found');
          }
        } catch (error) {
          console.error('Failed to restore auth state:', error);
          // Clear corrupted data
          localStorage.removeItem('auth_token');
          localStorage.removeItem('auth_user');
          localStorage.removeItem('auth_remember');
        }
      }
    },

    // Mock login for development/demo
    async mockLogin(role: 'admin' | 'worker' | 'distributor' | 'user' = 'user') {
      const mockUsers = {
        admin: { email: '<EMAIL>', password: 'admin123' },
        worker: { email: '<EMAIL>', password: 'worker123' },
        distributor: { email: '<EMAIL>', password: 'dist123' },
        user: { email: '<EMAIL>', password: 'user123' }
      };
      
      const credentials = mockUsers[role];
      return await this.login(credentials.email, credentials.password);
    }
  }
});
