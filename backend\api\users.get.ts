// TODO: backend - Replace with real database integration
export default defineEventHandler(async (event) => {
  // Mock users data
  const users = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "admin",
      balance: {
        usd: 5000.00,
        eur: 4200.00,
        btc: 0.15
      },
      isActive: true,
      isBanned: false,
      createdAt: "2024-01-15T10:30:00Z",
      lastLogin: "2025-07-29T08:15:00Z"
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "worker",
      balance: {
        usd: 1200.00,
        eur: 1000.00,
        btc: 0.05
      },
      isActive: true,
      isBanned: false,
      createdAt: "2024-02-20T14:45:00Z",
      lastLogin: "2025-07-28T16:30:00Z"
    },
    {
      id: 3,
      name: "<PERSON> Di<PERSON>ributor",
      email: "<EMAIL>",
      role: "distributor",
      balance: {
        usd: 3500.00,
        eur: 2900.00,
        btc: 0.12
      },
      isActive: true,
      isBanned: false,
      createdAt: "2024-03-10T09:20:00Z",
      lastLogin: "2025-07-29T07:45:00Z"
    },
    {
      id: 4,
      name: "<PERSON> Customer",
      email: "<EMAIL>",
      role: "user",
      balance: {
        usd: 250.00,
        eur: 210.00,
        btc: 0.01
      },
      isActive: true,
      isBanned: false,
      createdAt: "2024-05-05T11:15:00Z",
      lastLogin: "2025-07-29T09:00:00Z"
    },
    {
      id: 5,
      name: "Bob Banned",
      email: "<EMAIL>",
      role: "user",
      balance: {
        usd: 0.00,
        eur: 0.00,
        btc: 0.00
      },
      isActive: false,
      isBanned: true,
      createdAt: "2024-04-12T13:30:00Z",
      lastLogin: "2025-06-15T10:20:00Z"
    }
  ];

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 80));

  return {
    success: true,
    data: users,
    total: users.length
  };
});
