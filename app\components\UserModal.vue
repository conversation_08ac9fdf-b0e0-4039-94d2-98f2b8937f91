<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-screen overflow-y-auto">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold">
          {{ isEditing ? 'Edit User' : 'Add New User' }}
        </h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Personal Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
              <input 
                v-model="form.firstName" 
                type="text" 
                required 
                class="input-field"
                placeholder="Enter first name"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
              <input 
                v-model="form.lastName" 
                type="text" 
                required 
                class="input-field"
                placeholder="Enter last name"
              />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input 
              v-model="form.email" 
              type="email" 
              required 
              class="input-field"
              placeholder="Enter email address"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
            <input 
              v-model="form.phone" 
              type="tel" 
              class="input-field"
              placeholder="Enter phone number"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
            <textarea 
              v-model="form.address" 
              rows="3" 
              class="input-field"
              placeholder="Enter address"
            ></textarea>
          </div>
        </div>

        <!-- Account Settings -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Account Settings</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
              <select v-model="form.role" required class="input-field">
                <option value="">Select Role</option>
                <option value="admin">Admin</option>
                <option value="worker">Worker</option>
                <option value="distributor">Distributor</option>
                <option value="user">Customer</option>
              </select>
              <p class="mt-1 text-xs text-gray-500">
                {{ getRoleDescription(form.role) }}
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select v-model="form.status" required class="input-field">
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
              </select>
            </div>
          </div>

          <!-- Password Section -->
          <div v-if="!isEditing">
            <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
            <input 
              v-model="form.password" 
              type="password" 
              required 
              minlength="8"
              class="input-field"
              placeholder="Enter password (min 8 characters)"
            />
          </div>

          <div v-if="!isEditing">
            <label class="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
            <input 
              v-model="form.confirmPassword" 
              type="password" 
              required 
              class="input-field"
              placeholder="Confirm password"
            />
          </div>
        </div>

        <!-- Permissions (for admin/worker roles) -->
        <div v-if="form.role === 'admin' || form.role === 'worker'">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Permissions</h3>
          
          <div class="space-y-3">
            <div class="flex items-center">
              <input 
                v-model="form.permissions.manageProducts" 
                type="checkbox" 
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label class="ml-2 text-sm text-gray-700">Manage Products</label>
            </div>
            
            <div class="flex items-center">
              <input 
                v-model="form.permissions.manageOrders" 
                type="checkbox" 
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label class="ml-2 text-sm text-gray-700">Manage Orders</label>
            </div>
            
            <div v-if="form.role === 'admin'" class="flex items-center">
              <input 
                v-model="form.permissions.manageUsers" 
                type="checkbox" 
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label class="ml-2 text-sm text-gray-700">Manage Users</label>
            </div>
            
            <div v-if="form.role === 'admin'" class="flex items-center">
              <input 
                v-model="form.permissions.manageCategories" 
                type="checkbox" 
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label class="ml-2 text-sm text-gray-700">Manage Categories</label>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="text-red-600 text-sm">
          {{ error }}
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button 
            type="button"
            @click="$emit('close')"
            class="btn-secondary"
          >
            Cancel
          </button>
          <button 
            type="submit" 
            :disabled="isLoading"
            class="btn-primary"
          >
            {{ isLoading ? 'Saving...' : (isEditing ? 'Update User' : 'Create User') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  user?: any;
  isEditing?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['close', 'success']);

// Form data
const form = reactive({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  address: '',
  role: '',
  status: 'active',
  password: '',
  confirmPassword: '',
  permissions: {
    manageProducts: false,
    manageOrders: false,
    manageUsers: false,
    manageCategories: false
  }
});

const error = ref('');
const isLoading = ref(false);

// Initialize form with user data if editing
if (props.isEditing && props.user) {
  Object.assign(form, {
    firstName: props.user.firstName,
    lastName: props.user.lastName,
    email: props.user.email,
    phone: props.user.phone || '',
    address: props.user.address || '',
    role: props.user.role,
    status: props.user.status,
    permissions: props.user.permissions || {
      manageProducts: false,
      manageOrders: false,
      manageUsers: false,
      manageCategories: false
    }
  });
}

// Methods
const getRoleDescription = (role: string) => {
  const descriptions = {
    admin: 'Full access to all features and settings',
    worker: 'Can manage products and orders',
    distributor: 'Can view and manage assigned products',
    user: 'Standard customer account'
  };
  return descriptions[role as keyof typeof descriptions] || '';
};

const handleSubmit = async () => {
  error.value = '';

  // Validation
  if (!props.isEditing) {
    if (form.password !== form.confirmPassword) {
      error.value = 'Passwords do not match.';
      return;
    }
    
    if (form.password.length < 8) {
      error.value = 'Password must be at least 8 characters long.';
      return;
    }
  }

  isLoading.value = true;

  try {
    const userData = {
      firstName: form.firstName,
      lastName: form.lastName,
      email: form.email,
      phone: form.phone,
      address: form.address,
      role: form.role,
      status: form.status,
      permissions: form.permissions
    };

    if (!props.isEditing) {
      userData.password = form.password;
    }

    // TODO: backend - Implement user create/update API
    if (props.isEditing) {
      console.log('Updating user:', props.user.id, userData);
    } else {
      console.log('Creating user:', userData);
    }

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    emit('success');
  } catch (err) {
    error.value = 'Failed to save user. Please try again.';
  } finally {
    isLoading.value = false;
  }
};

// Auto-set permissions based on role
watch(() => form.role, (newRole) => {
  if (newRole === 'admin') {
    form.permissions = {
      manageProducts: true,
      manageOrders: true,
      manageUsers: true,
      manageCategories: true
    };
  } else if (newRole === 'worker') {
    form.permissions = {
      manageProducts: true,
      manageOrders: true,
      manageUsers: false,
      manageCategories: false
    };
  } else {
    form.permissions = {
      manageProducts: false,
      manageOrders: false,
      manageUsers: false,
      manageCategories: false
    };
  }
});
</script>
