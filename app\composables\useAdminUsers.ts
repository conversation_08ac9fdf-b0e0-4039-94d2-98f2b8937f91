import type { AdminUser } from '~/stores/admin';

export const useAdminUsers = () => {
  const adminStore = useAdminStore();
  
  // ===== REACTIVE STATE =====
  const users = computed(() => adminStore.users);
  const isLoading = computed(() => adminStore.isLoadingUsers);
  const filters = computed(() => adminStore.userFilters);
  const currentPage = computed(() => adminStore.currentPage);
  const itemsPerPage = computed(() => adminStore.itemsPerPage);
  const totalItems = computed(() => adminStore.totalItems);

  // ===== FILTERED USERS =====
  const filteredUsers = computed(() => {
    let filtered = [...adminStore.users];
    
    // Role filter
    if (filters.value.role) {
      filtered = filtered.filter(user => user.role === filters.value.role);
    }
    
    // Status filter
    if (filters.value.status) {
      filtered = filtered.filter(user => user.status === filters.value.status);
    }
    
    // Search filter
    if (filters.value.search) {
      const search = filters.value.search.toLowerCase();
      filtered = filtered.filter(user => 
        user.name.toLowerCase().includes(search) ||
        user.email.toLowerCase().includes(search)
      );
    }
    
    return filtered;
  });

  // ===== PAGINATION =====
  const paginatedUsers = computed(() => {
    const start = (currentPage.value - 1) * itemsPerPage.value;
    const end = start + itemsPerPage.value;
    return filteredUsers.value.slice(start, end);
  });

  const totalPages = computed(() => {
    return Math.ceil(filteredUsers.value.length / itemsPerPage.value);
  });

  // ===== ACTIONS =====
  const fetchUsers = async () => {
    adminStore.isLoadingUsers = true;
    try {
      // Mock API call - replace with real implementation
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Mock data
      const mockUsers: AdminUser[] = [
        {
          id: 1,
          name: 'أحمد محمد',
          email: '<EMAIL>',
          role: 'admin',
          walletBalance: 1500.00,
          status: 'active',
          createdAt: '2024-01-15T10:30:00Z',
          lastLogin: '2024-01-30T14:20:00Z',
          phone: '+966501234567',
          assignedOrders: 0,
          successRate: 100,
          performance: 'excellent'
        },
        {
          id: 2,
          name: 'فاطمة علي',
          email: '<EMAIL>',
          role: 'worker',
          walletBalance: 750.50,
          status: 'active',
          createdAt: '2024-01-10T09:15:00Z',
          lastLogin: '2024-01-30T16:45:00Z',
          phone: '+966507654321',
          assignedOrders: 45,
          successRate: 95,
          performance: 'excellent'
        },
        {
          id: 3,
          name: 'محمد السعيد',
          email: '<EMAIL>',
          role: 'worker',
          walletBalance: 320.25,
          status: 'active',
          createdAt: '2024-01-08T11:20:00Z',
          lastLogin: '2024-01-29T13:10:00Z',
          phone: '+966509876543',
          assignedOrders: 32,
          successRate: 88,
          performance: 'good'
        },
        {
          id: 4,
          name: 'سارة أحمد',
          email: '<EMAIL>',
          role: 'distributor',
          walletBalance: 2100.75,
          status: 'active',
          createdAt: '2024-01-05T08:45:00Z',
          lastLogin: '2024-01-30T12:30:00Z',
          phone: '+966502468135',
          assignedOrders: 0,
          successRate: 0,
          performance: 'excellent'
        },
        {
          id: 5,
          name: 'خالد عبدالله',
          email: '<EMAIL>',
          role: 'user',
          walletBalance: 125.00,
          status: 'active',
          createdAt: '2024-01-20T15:30:00Z',
          lastLogin: '2024-01-30T10:15:00Z',
          phone: '+966503691472'
        },
        {
          id: 6,
          name: 'نورا حسن',
          email: '<EMAIL>',
          role: 'user',
          walletBalance: 0.00,
          status: 'pending',
          createdAt: '2024-01-29T17:20:00Z',
          phone: '+966508529637'
        },
        {
          id: 7,
          name: 'عبدالرحمن محمد',
          email: '<EMAIL>',
          role: 'worker',
          walletBalance: 450.30,
          status: 'suspended',
          createdAt: '2024-01-12T12:10:00Z',
          lastLogin: '2024-01-25T09:45:00Z',
          phone: '+966504815926',
          assignedOrders: 18,
          successRate: 72,
          performance: 'average'
        }
      ];
      
      adminStore.users = mockUsers;
      adminStore.totalItems = mockUsers.length;
    } catch (error) {
      console.error('Failed to fetch users:', error);
      throw error;
    } finally {
      adminStore.isLoadingUsers = false;
    }
  };

  const updateUserRole = async (userId: number, newRole: AdminUser['role']) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const userIndex = adminStore.users.findIndex(u => u.id === userId);
      if (userIndex !== -1) {
        adminStore.users[userIndex].role = newRole;
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to update user role:', error);
      throw error;
    }
  };

  const updateUserStatus = async (userId: number, newStatus: AdminUser['status']) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const userIndex = adminStore.users.findIndex(u => u.id === userId);
      if (userIndex !== -1) {
        adminStore.users[userIndex].status = newStatus;
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to update user status:', error);
      throw error;
    }
  };

  const deleteUser = async (userId: number) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const userIndex = adminStore.users.findIndex(u => u.id === userId);
      if (userIndex !== -1) {
        adminStore.users.splice(userIndex, 1);
        adminStore.totalItems--;
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to delete user:', error);
      throw error;
    }
  };

  // ===== FILTERS =====
  const setRoleFilter = (role: string) => {
    adminStore.userFilters.role = role;
    adminStore.currentPage = 1;
  };

  const setStatusFilter = (status: string) => {
    adminStore.userFilters.status = status;
    adminStore.currentPage = 1;
  };

  const setSearchFilter = (search: string) => {
    adminStore.userFilters.search = search;
    adminStore.currentPage = 1;
  };

  const clearFilters = () => {
    adminStore.userFilters = {
      role: '',
      status: '',
      search: ''
    };
    adminStore.currentPage = 1;
  };

  // ===== PAGINATION =====
  const setPage = (page: number) => {
    adminStore.currentPage = page;
  };

  const setItemsPerPage = (items: number) => {
    adminStore.itemsPerPage = items;
    adminStore.currentPage = 1;
  };

  // ===== UTILITY FUNCTIONS =====
  const getRoleLabel = (role: AdminUser['role']): string => {
    const labels = {
      admin: 'مدير',
      worker: 'موظف',
      distributor: 'موزع',
      user: 'عميل'
    };
    return labels[role];
  };

  const getStatusLabel = (status: AdminUser['status']): string => {
    const labels = {
      active: 'نشط',
      suspended: 'معلق',
      pending: 'في الانتظار'
    };
    return labels[status];
  };

  const getPerformanceLabel = (performance?: AdminUser['performance']): string => {
    if (!performance) return '-';
    const labels = {
      excellent: 'ممتاز',
      good: 'جيد',
      average: 'متوسط',
      poor: 'ضعيف'
    };
    return labels[performance];
  };

  const formatWalletBalance = (balance: number, currencyCode: string = 'USD'): string => {
    return adminStore.formatCurrency(balance, currencyCode);
  };

  return {
    // State
    users,
    isLoading,
    filters,
    currentPage,
    itemsPerPage,
    totalItems,
    
    // Computed
    filteredUsers,
    paginatedUsers,
    totalPages,
    
    // Actions
    fetchUsers,
    updateUserRole,
    updateUserStatus,
    deleteUser,
    
    // Filters
    setRoleFilter,
    setStatusFilter,
    setSearchFilter,
    clearFilters,
    
    // Pagination
    setPage,
    setItemsPerPage,
    
    // Utilities
    getRoleLabel,
    getStatusLabel,
    getPerformanceLabel,
    formatWalletBalance
  };
};
