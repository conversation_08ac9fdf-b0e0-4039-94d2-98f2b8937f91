<template>
  <div class="admin-table-container">
    <!-- Table Header with Search and Filters -->
    <div class="admin-table-header" v-if="showHeader">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
        <div class="flex items-center gap-2">
          <h3 class="text-lg font-semibold text-theme-primary arabic-text">{{ title }}</h3>
          <span v-if="totalItems" class="text-sm text-theme-muted">
            ({{ totalItems }} {{ itemLabel }})
          </span>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-2">
          <!-- Search -->
          <div v-if="searchable" class="relative">
            <input
              v-model="searchQuery"
              type="text"
              :placeholder="searchPlaceholder"
              class="input-field pr-10 w-full sm:w-64 arabic-text"
              @input="$emit('search', searchQuery)"
            >
            <svg class="w-5 h-5 absolute right-3 top-1/2 transform -translate-y-1/2 text-theme-muted" 
                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <!-- Action Buttons -->
          <div class="flex gap-2">
            <button
              v-if="exportable"
              @click="$emit('export')"
              :disabled="isLoading"
              class="btn-secondary text-sm px-3 py-2 arabic-text"
            >
              <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              تصدير
            </button>
            
            <button
              v-if="creatable"
              @click="$emit('create')"
              class="btn-primary text-sm px-3 py-2 arabic-text"
            >
              <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ createLabel }}
            </button>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div v-if="$slots.filters" class="mb-4">
        <slot name="filters" />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="admin-table">
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-theme-primary"></div>
        <span class="mr-3 text-theme-secondary arabic-text">جاري التحميل...</span>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!items.length" class="admin-table">
      <div class="flex flex-col items-center justify-center py-12">
        <svg class="w-12 h-12 text-theme-muted mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
        </svg>
        <p class="text-theme-muted arabic-text">{{ emptyMessage }}</p>
      </div>
    </div>

    <!-- Table -->
    <div v-else class="admin-table overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr>
            <th v-for="column in columns" :key="column.key" 
                :class="[
                  'whitespace-nowrap',
                  column.sortable ? 'cursor-pointer hover:bg-theme-surface-light' : '',
                  column.align === 'center' ? 'text-center' : column.align === 'left' ? 'text-left' : 'text-right'
                ]"
                @click="column.sortable && handleSort(column.key)">
              <div class="flex items-center justify-between">
                <span class="arabic-text">{{ column.label }}</span>
                <svg v-if="column.sortable" class="w-4 h-4 text-theme-muted" 
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                </svg>
              </div>
            </th>
            <th v-if="actions.length" class="text-center">الإجراءات</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in items" :key="getItemKey(item, index)">
            <td v-for="column in columns" :key="column.key"
                :class="[
                  column.align === 'center' ? 'text-center' : column.align === 'left' ? 'text-left' : 'text-right'
                ]">
              <slot :name="`cell-${column.key}`" :item="item" :value="getNestedValue(item, column.key)">
                <span class="arabic-text">{{ formatCellValue(item, column) }}</span>
              </slot>
            </td>
            <td v-if="actions.length" class="text-center">
              <div class="flex items-center justify-center gap-2">
                <button
                  v-for="action in actions"
                  :key="action.key"
                  @click="$emit('action', { action: action.key, item })"
                  :class="[
                    'p-2 rounded-lg transition-colors',
                    action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                    action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' :
                    'text-theme-primary hover:bg-theme-surface-light'
                  ]"
                  :title="action.label"
                >
                  <component :is="action.icon" class="w-4 h-4" />
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div v-if="paginated && totalPages > 1" class="mt-4">
      <AdminPagination
        :current-page="currentPage"
        :total-pages="totalPages"
        :total-items="totalItems"
        :items-per-page="itemsPerPage"
        @page-change="$emit('page-change', $event)"
        @items-per-page-change="$emit('items-per-page-change', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Column {
  key: string;
  label: string;
  sortable?: boolean;
  align?: 'left' | 'center' | 'right';
  format?: (value: any, item: any) => string;
}

interface Action {
  key: string;
  label: string;
  icon: any;
  variant?: 'default' | 'danger' | 'warning';
}

interface Props {
  title?: string;
  items: any[];
  columns: Column[];
  actions?: Action[];
  isLoading?: boolean;
  searchable?: boolean;
  searchPlaceholder?: string;
  exportable?: boolean;
  creatable?: boolean;
  createLabel?: string;
  paginated?: boolean;
  currentPage?: number;
  totalPages?: number;
  totalItems?: number;
  itemsPerPage?: number;
  emptyMessage?: string;
  itemLabel?: string;
  showHeader?: boolean;
  keyField?: string;
}

const props = withDefaults(defineProps<Props>(), {
  actions: () => [],
  isLoading: false,
  searchable: true,
  searchPlaceholder: 'البحث...',
  exportable: false,
  creatable: false,
  createLabel: 'إضافة جديد',
  paginated: false,
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: 20,
  emptyMessage: 'لا توجد بيانات للعرض',
  itemLabel: 'عنصر',
  showHeader: true,
  keyField: 'id'
});

const emit = defineEmits<{
  search: [query: string];
  sort: [column: string];
  action: [payload: { action: string; item: any }];
  create: [];
  export: [];
  'page-change': [page: number];
  'items-per-page-change': [items: number];
}>();

const searchQuery = ref('');

const getItemKey = (item: any, index: number): string | number => {
  return item[props.keyField] || index;
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

const formatCellValue = (item: any, column: Column): string => {
  const value = getNestedValue(item, column.key);
  
  if (column.format) {
    return column.format(value, item);
  }
  
  if (value === null || value === undefined) {
    return '-';
  }
  
  return String(value);
};

const handleSort = (column: string) => {
  emit('sort', column);
};
</script>

<style scoped>
.admin-table-container {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--glass-shadow);
  overflow: hidden;
  animation: slideInUp 0.6s ease-out;
}

.admin-table-header {
  @apply p-6;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1));
  border-bottom: 1px solid var(--glass-border);
}

.admin-table {
  background: transparent;
  overflow: hidden;
}

.admin-table th {
  @apply font-bold py-4 px-6 text-right;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.05), rgba(59, 130, 246, 0.05));
  color: rgb(var(--color-text-primary));
  border-bottom: 1px solid var(--glass-border);
  font-family: 'Cairo', sans-serif;
  font-weight: 700;
  position: relative;
}

.admin-table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.admin-table th:hover::after {
  opacity: 1;
}

.admin-table td {
  @apply py-4 px-6;
  color: rgb(var(--color-text-secondary));
  border-bottom: 1px solid rgba(75, 85, 99, 0.2);
  transition: all 0.3s ease;
  font-family: 'Cairo', sans-serif;
}

.admin-table tr {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.admin-table tr::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.05), rgba(59, 130, 246, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.admin-table tr:hover::before {
  opacity: 1;
}

.admin-table tr:hover {
  transform: translateX(-2px);
  box-shadow: 4px 0 16px rgba(var(--color-primary), 0.1);
}

.admin-table tr:hover td {
  color: rgb(var(--color-text-primary));
}

.input-field {
  @apply w-full px-4 py-3 rounded-xl transition-all duration-300;
  background: rgba(31, 41, 55, 0.5);
  border: 1px solid var(--glass-border);
  color: rgb(var(--color-text-primary));
  font-family: 'Cairo', sans-serif;
  backdrop-filter: blur(10px);
}

.input-field:focus {
  outline: none;
  border-color: rgb(var(--color-primary));
  box-shadow: 0 0 0 3px rgba(var(--color-primary), 0.1);
  background: rgba(31, 41, 55, 0.7);
}

.input-field::placeholder {
  color: rgb(var(--color-text-muted));
  font-family: 'Cairo', sans-serif;
}

.btn-primary {
  @apply font-semibold py-3 px-6 rounded-xl transition-all duration-300;
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  color: white;
  border: none;
  box-shadow: 0 4px 16px rgba(var(--color-primary), 0.3);
  font-family: 'Cairo', sans-serif;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--color-primary), 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  @apply font-semibold py-3 px-6 rounded-xl transition-all duration-300;
  background: rgba(147, 51, 234, 0.1);
  color: rgb(var(--color-primary));
  border: 1px solid var(--glass-border);
  font-family: 'Cairo', sans-serif;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(147, 51, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--color-primary), 0.2);
  border-color: rgb(var(--color-primary));
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading Animation */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-table-header {
    @apply p-4;
  }

  .admin-table th,
  .admin-table td {
    @apply py-3 px-4 text-sm;
  }

  .btn-primary,
  .btn-secondary {
    @apply py-2 px-4 text-sm;
  }

  .input-field {
    @apply py-2 px-3 text-sm;
  }
}

/* RTL Support */
[dir="rtl"] .admin-table tr:hover {
  transform: translateX(2px);
}

[dir="rtl"] .admin-table th,
[dir="rtl"] .admin-table td {
  text-align: right;
}
</style>
