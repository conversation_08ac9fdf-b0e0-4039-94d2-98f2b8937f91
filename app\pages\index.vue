<template>
  <div class="min-h-screen bg-theme-background" dir="rtl">
    <!-- Promotional Banner -->
    <section class="container mx-auto px-4 pt-2 pb-6">
      <PromoBanner />
    </section>

    <!-- Categories Section -->
    <section class="container mx-auto px-4 pb-8">
      <GridView
        :items="categories"
        type="categories"
        title="تصفح الفئات"
        :show-view-all="false"
      />
    </section>

    <!-- Featured Products Section -->
    <section class="container mx-auto px-4 pb-8">
      <GridView
        :items="featuredProducts"
        type="products"
        title="المنتجات المميزة"
        :limit="6"
        view-all-link="/shop?filter=featured"
      />
    </section>

    <!-- Popular Products Section -->
    <section class="container mx-auto px-4 pb-8">
      <GridView
        :items="popularProducts"
        type="products"
        title="الأكثر شعبية"
        :limit="6"
        view-all-link="/shop?filter=popular"
      />
    </section>
  </div>
</template>

<script setup lang="ts">
import { mockProducts, mockCategories } from '~/data/mockData'

// SEO Meta
useSeoMeta({
  title: 'بنتاكون - متجر الألعاب الرقمية',
  description: 'أفضل متجر للألعاب الرقمية وبطاقات الهدايا في الشرق الأوسط',
  ogTitle: 'بنتاكون - متجر الألعاب الرقمية',
  ogDescription: 'أفضل متجر للألعاب الرقمية وبطاقات الهدايا في الشرق الأوسط',
  ogImage: '/og-image.jpg',
  twitterCard: 'summary_large_image',
})

// Data
const categories = mockCategories
const featuredProducts = mockProducts.filter(product => product.featured)
const popularProducts = mockProducts.filter(product => product.popular)
</script>
