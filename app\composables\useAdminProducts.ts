import type { AdminProduct } from '~/stores/admin';

export const useAdminProducts = () => {
  const adminStore = useAdminStore();
  
  // ===== REACTIVE STATE =====
  const products = computed(() => adminStore.products);
  const isLoading = computed(() => adminStore.isLoadingProducts);
  const filters = computed(() => adminStore.productFilters);

  // ===== FILTERED PRODUCTS =====
  const filteredProducts = computed(() => {
    let filtered = [...adminStore.products];
    
    // Type filter
    if (filters.value.type) {
      filtered = filtered.filter(product => product.type === filters.value.type);
    }
    
    // Status filter
    if (filters.value.status) {
      filtered = filtered.filter(product => product.status === filters.value.status);
    }
    
    // Category filter
    if (filters.value.category) {
      filtered = filtered.filter(product => product.category === filters.value.category);
    }
    
    // Search filter
    if (filters.value.search) {
      const search = filters.value.search.toLowerCase();
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(search) ||
        product.nameAr.toLowerCase().includes(search) ||
        (product.description && product.description.toLowerCase().includes(search)) ||
        (product.descriptionAr && product.descriptionAr.toLowerCase().includes(search))
      );
    }
    
    return filtered;
  });

  // ===== ACTIONS =====
  const fetchProducts = async () => {
    adminStore.isLoadingProducts = true;
    try {
      // Mock API call - replace with real implementation
      await new Promise(resolve => setTimeout(resolve, 600));
      
      // Mock data
      const mockProducts: AdminProduct[] = [
        {
          id: 1,
          name: 'Gaming Package Premium',
          nameAr: 'باقة الألعاب المميزة',
          type: 'package',
          basePriceUSD: 49.99,
          status: 'active',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-30T14:20:00Z',
          description: 'Premium gaming package with multiple games',
          descriptionAr: 'باقة ألعاب مميزة تحتوي على عدة ألعاب',
          category: 'gaming',
          categoryAr: 'ألعاب',
          currencyPrices: {
            SAR: 187.50,
            EUR: 42.49
          }
        },
        {
          id: 2,
          name: 'Steam Gift Card',
          nameAr: 'بطاقة هدايا ستيم',
          type: 'code',
          basePriceUSD: 20.00,
          status: 'active',
          createdAt: '2024-01-10T09:15:00Z',
          updatedAt: '2024-01-29T16:45:00Z',
          description: 'Steam digital gift card',
          descriptionAr: 'بطاقة هدايا رقمية لمتجر ستيم',
          category: 'gift-cards',
          categoryAr: 'بطاقات الهدايا',
          codes: ['STEAM-ABC123', 'STEAM-DEF456', 'STEAM-GHI789'],
          codesUsed: 15,
          codesTotal: 100,
          currencyPrices: {
            SAR: 75.00,
            EUR: 17.00
          }
        },
        {
          id: 3,
          name: 'PlayStation Plus Subscription',
          nameAr: 'اشتراك بلايستيشن بلس',
          type: 'simple',
          basePriceUSD: 59.99,
          status: 'active',
          createdAt: '2024-01-08T11:20:00Z',
          updatedAt: '2024-01-28T13:10:00Z',
          description: '12-month PlayStation Plus subscription',
          descriptionAr: 'اشتراك بلايستيشن بلس لمدة 12 شهر',
          category: 'subscriptions',
          categoryAr: 'اشتراكات',
          currencyPrices: {
            SAR: 224.96,
            EUR: 50.99
          }
        },
        {
          id: 4,
          name: 'Netflix Premium Account',
          nameAr: 'حساب نتفليكس مميز',
          type: 'simple',
          basePriceUSD: 15.99,
          status: 'inactive',
          createdAt: '2024-01-05T08:45:00Z',
          updatedAt: '2024-01-25T12:30:00Z',
          description: 'Netflix premium account access',
          descriptionAr: 'وصول لحساب نتفليكس مميز',
          category: 'streaming',
          categoryAr: 'بث مباشر',
          currencyPrices: {
            SAR: 59.96,
            EUR: 13.59
          }
        },
        {
          id: 5,
          name: 'Mobile Game Currency',
          nameAr: 'عملة الألعاب المحمولة',
          type: 'code',
          basePriceUSD: 9.99,
          status: 'active',
          createdAt: '2024-01-20T15:30:00Z',
          updatedAt: '2024-01-30T10:15:00Z',
          description: 'In-game currency for mobile games',
          descriptionAr: 'عملة داخل اللعبة للألعاب المحمولة',
          category: 'gaming',
          categoryAr: 'ألعاب',
          codes: ['MOBILE-XYZ789', 'MOBILE-ABC123'],
          codesUsed: 8,
          codesTotal: 50,
          currencyPrices: {
            SAR: 37.46,
            EUR: 8.49
          }
        }
      ];
      
      adminStore.products = mockProducts;
    } catch (error) {
      console.error('Failed to fetch products:', error);
      throw error;
    } finally {
      adminStore.isLoadingProducts = false;
    }
  };

  const createProduct = async (productData: Partial<AdminProduct>) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newProduct: AdminProduct = {
        id: Date.now(),
        name: productData.name || '',
        nameAr: productData.nameAr || '',
        type: productData.type || 'simple',
        basePriceUSD: productData.basePriceUSD || 0,
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        description: productData.description,
        descriptionAr: productData.descriptionAr,
        category: productData.category,
        categoryAr: productData.categoryAr,
        currencyPrices: productData.currencyPrices || {},
        codes: productData.codes || [],
        codesUsed: 0,
        codesTotal: productData.codes?.length || 0
      };
      
      adminStore.products.push(newProduct);
      return { success: true, product: newProduct };
    } catch (error) {
      console.error('Failed to create product:', error);
      throw error;
    }
  };

  const updateProduct = async (productId: number, productData: Partial<AdminProduct>) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 400));
      
      const productIndex = adminStore.products.findIndex(p => p.id === productId);
      if (productIndex !== -1) {
        adminStore.products[productIndex] = {
          ...adminStore.products[productIndex],
          ...productData,
          updatedAt: new Date().toISOString()
        };
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to update product:', error);
      throw error;
    }
  };

  const deleteProduct = async (productId: number) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const productIndex = adminStore.products.findIndex(p => p.id === productId);
      if (productIndex !== -1) {
        adminStore.products.splice(productIndex, 1);
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to delete product:', error);
      throw error;
    }
  };

  const updateProductStatus = async (productId: number, status: AdminProduct['status']) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const productIndex = adminStore.products.findIndex(p => p.id === productId);
      if (productIndex !== -1) {
        adminStore.products[productIndex].status = status;
        adminStore.products[productIndex].updatedAt = new Date().toISOString();
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to update product status:', error);
      throw error;
    }
  };

  // ===== FILTERS =====
  const setTypeFilter = (type: string) => {
    adminStore.productFilters.type = type;
  };

  const setStatusFilter = (status: string) => {
    adminStore.productFilters.status = status;
  };

  const setCategoryFilter = (category: string) => {
    adminStore.productFilters.category = category;
  };

  const setSearchFilter = (search: string) => {
    adminStore.productFilters.search = search;
  };

  const clearFilters = () => {
    adminStore.productFilters = {
      type: '',
      status: '',
      category: '',
      search: ''
    };
  };

  // ===== UTILITY FUNCTIONS =====
  const getTypeLabel = (type: AdminProduct['type']): string => {
    const labels = {
      simple: 'منتج بسيط',
      package: 'باقة',
      code: 'كود رقمي'
    };
    return labels[type];
  };

  const getStatusLabel = (status: AdminProduct['status']): string => {
    const labels = {
      active: 'نشط',
      inactive: 'غير نشط',
      archived: 'مؤرشف'
    };
    return labels[status];
  };

  const formatPrice = (priceUSD: number, currencyCode: string = 'USD'): string => {
    if (currencyCode === 'USD') {
      return `$${priceUSD.toFixed(2)}`;
    }
    
    const convertedPrice = adminStore.convertFromUSD(priceUSD, currencyCode);
    return adminStore.formatCurrency(convertedPrice, currencyCode);
  };

  const getCodesAvailable = (product: AdminProduct): number => {
    if (product.type !== 'code') return 0;
    return (product.codesTotal || 0) - (product.codesUsed || 0);
  };

  const getCodesPercentage = (product: AdminProduct): number => {
    if (product.type !== 'code' || !product.codesTotal) return 0;
    return ((product.codesUsed || 0) / product.codesTotal) * 100;
  };

  return {
    // State
    products,
    isLoading,
    filters,
    
    // Computed
    filteredProducts,
    
    // Actions
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    updateProductStatus,
    
    // Filters
    setTypeFilter,
    setStatusFilter,
    setCategoryFilter,
    setSearchFilter,
    clearFilters,
    
    // Utilities
    getTypeLabel,
    getStatusLabel,
    formatPrice,
    getCodesAvailable,
    getCodesPercentage
  };
};
