// TODO: backend - Replace with real database service (Supabase, Prisma, etc.)

export interface User {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'worker' | 'distributor' | 'user';
  balance: {
    usd: number;
    eur: number;
    btc: number;
  };
  isActive: boolean;
  isBanned: boolean;
  createdAt: string;
  lastLogin: string;
}

export interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  category: string;
  image: string;
  stock: number;
  packages: Package[];
  customFields: CustomField[];
  digitalCodes: string[];
}

export interface Package {
  id: number;
  name: string;
  price: number;
  features: string[];
}

export interface CustomField {
  name: string;
  type: 'text' | 'select' | 'number';
  options?: string[];
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  description: string;
  image: string;
  productCount: number;
  isActive: boolean;
}

export interface Order {
  id: number;
  userId: number;
  productId: number;
  productName: string;
  packageName: string;
  quantity: number;
  totalPrice: number;
  currency: string;
  status: 'pending' | 'completed' | 'declined';
  profit: number;
  declineNote: string | null;
  digitalCode: string | null;
  customFields: Record<string, string>;
  createdAt: string;
  completedAt: string | null;
}

export interface Transaction {
  id: number;
  userId: number;
  type: 'deposit' | 'purchase' | 'profit' | 'withdrawal';
  amount: number;
  currency: string;
  description: string;
  status: 'pending' | 'completed' | 'failed';
  orderId?: number;
  createdAt: string;
  completedAt: string | null;
}

// Mock database service
export class DatabaseService {
  // TODO: Replace with real database connection
  static async connect() {
    console.log('Mock database connected');
  }

  static async disconnect() {
    console.log('Mock database disconnected');
  }

  // User operations
  static async getUsers(): Promise<User[]> {
    // TODO: Implement real database query
    throw new Error('Not implemented - use API endpoints');
  }

  static async getUserById(id: number): Promise<User | null> {
    // TODO: Implement real database query
    throw new Error('Not implemented - use API endpoints');
  }

  // Product operations
  static async getProducts(): Promise<Product[]> {
    // TODO: Implement real database query
    throw new Error('Not implemented - use API endpoints');
  }

  static async getProductById(id: number): Promise<Product | null> {
    // TODO: Implement real database query
    throw new Error('Not implemented - use API endpoints');
  }

  // Order operations
  static async getOrders(): Promise<Order[]> {
    // TODO: Implement real database query
    throw new Error('Not implemented - use API endpoints');
  }

  static async createOrder(order: Omit<Order, 'id' | 'createdAt'>): Promise<Order> {
    // TODO: Implement real database query
    throw new Error('Not implemented - use API endpoints');
  }
}
