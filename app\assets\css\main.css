/* Import Cairo Arabic Font */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== CENTRALIZED THEME SYSTEM ===== */
/* Single source of truth for all colors and design tokens */
@layer base {
  :root {
    /* === CORE COLORS === */
    --color-background: 17 24 39;        /* gray-900 */
    --color-surface: 31 41 55;           /* gray-800 */
    --color-surface-light: 55 65 81;     /* gray-700 */
    --color-surface-lighter: 75 85 99;   /* gray-600 */

    --color-text-primary: 255 255 255;   /* white */
    --color-text-secondary: 209 213 219; /* gray-300 */
    --color-text-muted: 156 163 175;     /* gray-400 */

    --color-border: 75 85 99;             /* gray-600 */
    --color-border-light: 55 65 81;      /* gray-700 */
    --color-border-lighter: 31 41 55;    /* gray-800 */

    /* === ACCENT COLORS === */
    --color-primary: 147 51 234;         /* purple-600 */
    --color-primary-hover: 126 34 206;   /* purple-700 */
    --color-secondary: 59 130 246;       /* blue-600 */
    --color-secondary-hover: 37 99 235;  /* blue-700 */

    --color-accent: 236 72 153;          /* pink-600 */
    --color-accent-hover: 219 39 119;    /* pink-700 */

    /* === STATUS COLORS === */
    --color-success: 34 197 94;          /* green-500 */
    --color-warning: 245 158 11;         /* amber-500 */
    --color-error: 239 68 68;            /* red-500 */
    --color-info: 59 130 246;            /* blue-500 */

    /* === SPECIAL COLORS === */
    --color-rating: 251 191 36;          /* yellow-400 */
    --color-discount: 239 68 68;         /* red-500 */

    /* === OPACITY VALUES === */
    --opacity-light: 0.1;
    --opacity-medium: 0.3;
    --opacity-strong: 0.5;
    --opacity-backdrop: 0.8;

    /* === GLASSMORPHISM === */
    --glass-bg: rgba(31, 41, 55, 0.5);
    --glass-bg-hover: rgba(31, 41, 55, 0.6);
    --glass-border: rgba(75, 85, 99, 0.3);
    --glass-border-hover: rgba(147, 51, 234, 0.3);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --glass-shadow-hover: 0 8px 32px rgba(147, 51, 234, 0.1);

    /* === SPACING & SIZING === */
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;

    /* === TRANSITIONS === */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
  }

  html {
    font-family: 'Cairo', 'Inter', system-ui, sans-serif;
  }

  body {
    background-color: rgb(var(--color-background));
    color: rgb(var(--color-text-primary));
    font-family: 'Cairo', 'Inter', system-ui, sans-serif;
  }

  /* Ensure all text elements use Cairo font */
  * {
    font-family: 'Cairo', 'Inter', system-ui, sans-serif;
  }
}

/* ===== CENTRALIZED COMPONENT STYLES ===== */
/* All components use theme variables - single source of truth */
@layer components {

  /* === BUTTONS === */
  .btn-primary {
    background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
    color: rgb(var(--color-text-primary));
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-xl);
    transition: all var(--transition-normal);
    box-shadow: 0 4px 16px rgba(var(--color-primary), 0.25);
    transform-origin: center;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, rgb(var(--color-primary-hover)), rgb(var(--color-secondary-hover)));
    box-shadow: 0 6px 20px rgba(var(--color-primary), 0.35);
    transform: scale(0.98);
  }

  .btn-secondary {
    background: rgba(var(--color-surface-light), var(--opacity-strong));
    backdrop-filter: blur(8px);
    color: rgb(var(--color-text-primary));
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-xl);
    border: 1px solid rgba(var(--color-border), var(--opacity-strong));
    transition: all var(--transition-normal);
    transform-origin: center;
  }

  .btn-secondary:hover {
    background: rgba(var(--color-surface-lighter), var(--opacity-strong));
    border-color: rgba(var(--color-border-light), var(--opacity-strong));
    transform: scale(0.98);
  }

  .btn-danger {
    background: rgb(var(--color-error));
    color: rgb(var(--color-text-primary));
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-xl);
    transition: all var(--transition-fast);
  }

  .btn-danger:hover {
    background: rgba(var(--color-error), 0.8);
  }

  /* === CARDS === */
  .card {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--glass-shadow);
    padding: 1.5rem;
    transition: all var(--transition-normal);
  }

  .card:hover {
    background: var(--glass-bg-hover);
    border-color: var(--glass-border-hover);
    box-shadow: var(--glass-shadow-hover);
  }

  /* === INPUTS === */
  .input-field {
    width: 100%;
    padding: 0.75rem;
    background: rgba(var(--color-surface), var(--opacity-strong));
    border: 1px solid rgba(var(--color-border), var(--opacity-strong));
    border-radius: var(--border-radius-lg);
    color: rgb(var(--color-text-primary));
    backdrop-filter: blur(8px);
    transition: all var(--transition-fast);
  }

  .input-field::placeholder {
    color: rgb(var(--color-text-muted));
  }

  .input-field:focus {
    outline: none;
    border-color: rgb(var(--color-primary));
    box-shadow: 0 0 0 2px rgba(var(--color-primary), 0.2);
  }
  
  /* === THEME-BASED UTILITY CLASSES === */
  /* Use these instead of hardcoded Tailwind classes */

  /* Backgrounds */
  .bg-theme-surface { background-color: rgb(var(--color-surface)); }
  .bg-theme-surface-light { background-color: rgb(var(--color-surface-light)); }
  .bg-theme-glass { background: var(--glass-bg); backdrop-filter: blur(16px); }
  .bg-theme-glass-hover { background: var(--glass-bg-hover); }

  /* Text Colors */
  .text-theme-primary { color: rgb(var(--color-text-primary)); }
  .text-theme-secondary { color: rgb(var(--color-text-secondary)); }
  .text-theme-muted { color: rgb(var(--color-text-muted)); }
  .text-theme-accent { color: rgb(var(--color-primary)); }
  .text-theme-rating { color: rgb(var(--color-rating)); }

  /* Force white text for specific elements */
  .text-white-force { color: rgb(var(--color-text-primary)) !important; }
  .section-title {
    color: rgb(var(--color-text-primary)) !important;
    font-family: 'Cairo', 'Inter', system-ui, sans-serif;
    font-weight: 700;
  }
  .product-title {
    color: rgb(var(--color-text-primary)) !important;
    font-family: 'Cairo', 'Inter', system-ui, sans-serif;
  }
  .product-price {
    color: rgb(var(--color-text-primary)) !important;
    font-family: 'Cairo', 'Inter', system-ui, sans-serif;
    font-weight: 600;
  }

  /* Borders */
  .border-theme { border-color: rgba(var(--color-border), var(--opacity-medium)); }
  .border-theme-light { border-color: rgba(var(--color-border-light), var(--opacity-medium)); }
  .border-theme-hover { border-color: rgba(var(--color-primary), var(--opacity-medium)); }

  /* Product Card Styles */
  .product-card {
    background-color: rgb(var(--color-surface));
    border: 1px solid rgba(var(--color-border-light), var(--opacity-medium));
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-normal);
    overflow: hidden;
  }

  .product-card:hover {
    transform: scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(var(--color-primary), var(--opacity-medium));
  }

  /* Badges */
  .badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.625rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .badge-discount {
    background-color: rgb(var(--color-error));
    color: rgb(var(--color-text-primary));
  }

  .badge-featured {
    background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-accent)));
    color: rgb(var(--color-text-primary));
  }

  .badge-rating {
    color: rgb(var(--color-rating));
  }

  /* Status badges with theme colors */
  .status-pending {
    background-color: rgba(var(--color-warning), 0.1);
    color: rgb(var(--color-warning));
    border: 1px solid rgba(var(--color-warning), 0.2);
  }

  .status-completed {
    background-color: rgba(var(--color-success), 0.1);
    color: rgb(var(--color-success));
    border: 1px solid rgba(var(--color-success), 0.2);
  }

  .status-declined {
    background-color: rgba(var(--color-error), 0.1);
    color: rgb(var(--color-error));
    border: 1px solid rgba(var(--color-error), 0.2);
  }

  /* Pagination Styles */
  .pagination-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--glass-border);
    background: rgba(var(--color-surface), var(--opacity-medium));
    color: rgb(var(--color-text-secondary));
    transition: all var(--transition-fast);
  }

  .pagination-button:hover:not(:disabled) {
    background: rgba(var(--color-surface-light), var(--opacity-strong));
    border-color: rgba(var(--color-border), var(--opacity-strong));
  }

  .pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .pagination-button.active {
    border-color: rgb(var(--color-primary));
    background: rgba(var(--color-primary), 0.2);
    color: rgb(var(--color-primary));
    font-weight: bold;
  }

  /* === MOBILE SPECIFIC STYLES === */

  /* Mobile Logo Centering */
  .mobile-logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  /* Enhanced Mobile Navigation */
  .mobile-nav-backdrop {
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
  }

  .mobile-nav-drawer {
    background: rgba(var(--color-surface), 0.95);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(var(--color-border-light), 0.3);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  }

  .mobile-nav-item {
    color: rgb(var(--color-text-secondary));
    transition: all var(--transition-fast);
    border-radius: var(--border-radius-lg);
    font-family: 'Cairo', 'Inter', system-ui, sans-serif;
    font-weight: 500;
  }

  .mobile-nav-item:hover {
    color: rgb(var(--color-text-primary));
    background: rgba(var(--color-surface-light), 0.5);
    backdrop-filter: blur(8px);
  }

  /* Mobile Header Improvements */
  @media (max-width: 1023px) {
    .mobile-header-logo {
      justify-content: center !important;
      flex: 1;
    }

    .mobile-header-user {
      position: absolute;
      right: 1rem;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

/* Custom utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #CBD5E0 #F7FAFC;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #F7FAFC;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #CBD5E0;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #A0AEC0;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.5s ease-out;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Enhanced Button Styles */
.btn-secondary {
  background: rgba(var(--color-surface), 0.8);
  border: 1px solid rgba(var(--color-border-light), 0.3);
  color: rgb(var(--color-text-primary));
  font-family: 'Cairo', 'Inter', system-ui, sans-serif;
  font-weight: 500;
  border-radius: 0.75rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(var(--color-surface), 1);
  border-color: rgba(var(--color-accent), 0.5);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
}

/* Input Field Enhancements */
.input-field {
  background: rgba(var(--color-surface), 0.8);
  border: 1px solid rgba(var(--color-border-light), 0.3);
  color: rgb(var(--color-text-primary));
  font-family: 'Cairo', 'Inter', system-ui, sans-serif;
  border-radius: 0.75rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  width: 100%;
  padding: 0.75rem 1rem;
}

.input-field:focus {
  outline: none;
  border-color: rgb(var(--color-accent));
  box-shadow: 0 0 0 3px rgba(var(--color-accent), 0.1);
  background: rgba(var(--color-surface), 1);
}

.input-field::placeholder {
  color: rgb(var(--color-text-muted));
}

/* Card Hover Effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.4);
  border-color: rgba(var(--color-accent), 0.3);
}

/* Responsive improvements */
@media (max-width: 640px) {
  .card {
    @apply p-4;
  }

  .btn-primary,
  .btn-secondary,
  .btn-danger {
    @apply text-sm px-3 py-2;
  }
}
