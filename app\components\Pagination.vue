<template>
  <div v-if="totalPages > 1" class="flex items-center justify-center space-x-2 space-x-reverse">
    <!-- Previous Button -->
    <button
      @click="$emit('page-change', currentPage - 1)"
      :disabled="currentPage === 1"
      class="pagination-button"
      aria-label="الصفحة السابقة"
    >
      <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="m15 18-6-6 6-6"/>
      </svg>
    </button>

    <!-- Page Numbers -->
    <div class="flex items-center space-x-1 space-x-reverse">
      <template v-for="page in visiblePages" :key="page">
        <!-- Dots -->
        <span v-if="page === '...'" class="flex items-center justify-center w-10 h-10 text-theme-muted">
          ...
        </span>
        
        <!-- Page Number -->
        <button
          v-else
          @click="$emit('page-change', page)"
          :class="[
            'pagination-button',
            page === currentPage ? 'active' : ''
          ]"
          :aria-label="`الصفحة ${page}`"
          :aria-current="page === currentPage ? 'page' : undefined"
        >
          {{ page }}
        </button>
      </template>
    </div>

    <!-- Next Button -->
    <button
      @click="$emit('page-change', currentPage + 1)"
      :disabled="currentPage === totalPages"
      class="pagination-button"
      aria-label="الصفحة التالية"
    >
      <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="m9 18 6-6-6-6"/>
      </svg>
    </button>

    <!-- Page Info -->
    <div class="mr-4 text-sm text-gray-400">
      صفحة {{ currentPage }} من {{ totalPages }}
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  currentPage: number
  totalPages: number
}

const props = defineProps<Props>()

defineEmits<{
  'page-change': [page: number]
}>()

const visiblePages = computed(() => {
  const delta = 2
  const range = []
  const rangeWithDots = []

  for (let i = Math.max(2, props.currentPage - delta); i <= Math.min(props.totalPages - 1, props.currentPage + delta); i++) {
    range.push(i)
  }

  if (props.currentPage - delta > 2) {
    rangeWithDots.push(1, '...')
  } else {
    rangeWithDots.push(1)
  }

  rangeWithDots.push(...range)

  if (props.currentPage + delta < props.totalPages - 1) {
    rangeWithDots.push('...', props.totalPages)
  } else {
    rangeWithDots.push(props.totalPages)
  }

  return rangeWithDots
})
</script>
