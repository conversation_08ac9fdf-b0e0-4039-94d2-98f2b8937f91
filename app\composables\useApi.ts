/**
 * API composable for making HTTP requests
 * Provides a consistent interface for API calls with error handling
 */

interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: any;
  headers?: Record<string, string>;
  query?: Record<string, any>;
}

export const useApi = () => {
  const config = useRuntimeConfig();
  
  // Base API URL - TODO: backend - Replace with real API base URL
  const baseURL = config.public.apiBase || '/backend/api';

  /**
   * Make an API request
   */
  const request = async <T = any>(
    endpoint: string, 
    options: ApiOptions = {}
  ): Promise<ApiResponse<T>> => {
    const { method = 'GET', body, headers = {}, query } = options;

    try {
      // Build URL with query parameters
      let url = `${baseURL}${endpoint}`;
      if (query) {
        const searchParams = new URLSearchParams();
        Object.entries(query).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, String(value));
          }
        });
        const queryString = searchParams.toString();
        if (queryString) {
          url += `?${queryString}`;
        }
      }

      // Make the request
      const response = await $fetch<ApiResponse<T>>(url, {
        method,
        body: body ? JSON.stringify(body) : undefined,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      });

      return response;
    } catch (error: any) {
      console.error('API request failed:', error);
      
      return {
        data: null as T,
        success: false,
        error: error.message || 'An error occurred while making the request'
      };
    }
  };

  /**
   * GET request
   */
  const get = <T = any>(endpoint: string, query?: Record<string, any>) => {
    return request<T>(endpoint, { method: 'GET', query });
  };

  /**
   * POST request
   */
  const post = <T = any>(endpoint: string, body?: any) => {
    return request<T>(endpoint, { method: 'POST', body });
  };

  /**
   * PUT request
   */
  const put = <T = any>(endpoint: string, body?: any) => {
    return request<T>(endpoint, { method: 'PUT', body });
  };

  /**
   * DELETE request
   */
  const del = <T = any>(endpoint: string) => {
    return request<T>(endpoint, { method: 'DELETE' });
  };

  /**
   * PATCH request
   */
  const patch = <T = any>(endpoint: string, body?: any) => {
    return request<T>(endpoint, { method: 'PATCH', body });
  };

  return {
    request,
    get,
    post,
    put,
    delete: del,
    patch
  };
};

/**
 * Products API composable
 */
export const useProductsApi = () => {
  const api = useApi();

  const getProducts = (filters?: {
    category?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: string;
    page?: number;
    limit?: number;
  }) => {
    return api.get('/products', filters);
  };

  const getProduct = (id: number) => {
    return api.get(`/products/${id}`);
  };

  const createProduct = (productData: any) => {
    return api.post('/products', productData);
  };

  const updateProduct = (id: number, productData: any) => {
    return api.put(`/products/${id}`, productData);
  };

  const deleteProduct = (id: number) => {
    return api.delete(`/products/${id}`);
  };

  return {
    getProducts,
    getProduct,
    createProduct,
    updateProduct,
    deleteProduct
  };
};

/**
 * Orders API composable
 */
export const useOrdersApi = () => {
  const api = useApi();

  const getOrders = (filters?: {
    status?: string;
    userId?: number;
    dateFrom?: string;
    dateTo?: string;
    page?: number;
    limit?: number;
  }) => {
    return api.get('/orders', filters);
  };

  const getOrder = (id: number) => {
    return api.get(`/orders/${id}`);
  };

  const createOrder = (orderData: any) => {
    return api.post('/orders', orderData);
  };

  const updateOrderStatus = (id: number, status: string) => {
    return api.patch(`/orders/${id}/status`, { status });
  };

  const cancelOrder = (id: number) => {
    return api.patch(`/orders/${id}/cancel`);
  };

  return {
    getOrders,
    getOrder,
    createOrder,
    updateOrderStatus,
    cancelOrder
  };
};

/**
 * Users API composable
 */
export const useUsersApi = () => {
  const api = useApi();

  const getUsers = (filters?: {
    role?: string;
    status?: string;
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    return api.get('/users', filters);
  };

  const getUser = (id: number) => {
    return api.get(`/users/${id}`);
  };

  const createUser = (userData: any) => {
    return api.post('/users', userData);
  };

  const updateUser = (id: number, userData: any) => {
    return api.put(`/users/${id}`, userData);
  };

  const deleteUser = (id: number) => {
    return api.delete(`/users/${id}`);
  };

  const updateUserStatus = (id: number, status: string) => {
    return api.patch(`/users/${id}/status`, { status });
  };

  return {
    getUsers,
    getUser,
    createUser,
    updateUser,
    deleteUser,
    updateUserStatus
  };
};

/**
 * Categories API composable
 */
export const useCategoriesApi = () => {
  const api = useApi();

  const getCategories = () => {
    return api.get('/categories');
  };

  const getCategory = (id: number) => {
    return api.get(`/categories/${id}`);
  };

  const createCategory = (categoryData: any) => {
    return api.post('/categories', categoryData);
  };

  const updateCategory = (id: number, categoryData: any) => {
    return api.put(`/categories/${id}`, categoryData);
  };

  const deleteCategory = (id: number) => {
    return api.delete(`/categories/${id}`);
  };

  return {
    getCategories,
    getCategory,
    createCategory,
    updateCategory,
    deleteCategory
  };
};

/**
 * Wallet API composable
 */
export const useWalletApi = () => {
  const api = useApi();

  const getBalance = () => {
    return api.get('/wallet/balance');
  };

  const getTransactions = (filters?: {
    type?: string;
    currency?: string;
    dateFrom?: string;
    dateTo?: string;
    page?: number;
    limit?: number;
  }) => {
    return api.get('/wallet/transactions', filters);
  };

  const deposit = (amount: number, currency: string, paymentMethod: string) => {
    return api.post('/wallet/deposit', { amount, currency, paymentMethod });
  };

  const withdraw = (amount: number, currency: string, withdrawalMethod: string) => {
    return api.post('/wallet/withdraw', { amount, currency, withdrawalMethod });
  };

  return {
    getBalance,
    getTransactions,
    deposit,
    withdraw
  };
};
