import type { Currency } from '~/stores/admin';

export const useAdminCurrencies = () => {
  const adminStore = useAdminStore();
  
  // ===== REACTIVE STATE =====
  const currencies = computed(() => adminStore.currencies);
  const isLoading = computed(() => adminStore.isLoadingCurrencies);
  const activeCurrencies = computed(() => adminStore.activeCurrencies);
  const defaultCurrency = computed(() => adminStore.defaultCurrency);

  // ===== ACTIONS =====
  const fetchCurrencies = async () => {
    return await adminStore.fetchCurrencies();
  };

  const createCurrency = async (currencyData: Partial<Currency>) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Validate required fields
      if (!currencyData.code || !currencyData.symbol || !currencyData.exchangeRate) {
        throw new Error('الرجاء ملء جميع الحقول المطلوبة');
      }

      // Check if currency code already exists
      const existingCurrency = adminStore.currencies.find(c => c.code === currencyData.code);
      if (existingCurrency) {
        throw new Error('رمز العملة موجود بالفعل');
      }

      const newCurrency: Currency = {
        id: Date.now(),
        code: currencyData.code.toUpperCase(),
        symbol: currencyData.symbol,
        nameAr: currencyData.nameAr || '',
        nameEn: currencyData.nameEn || '',
        exchangeRate: currencyData.exchangeRate,
        status: 'active',
        isDefault: false, // Only USD can be default
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      adminStore.currencies.push(newCurrency);
      return { success: true, currency: newCurrency };
    } catch (error) {
      console.error('Failed to create currency:', error);
      throw error;
    }
  };

  const updateCurrency = async (currencyId: number, currencyData: Partial<Currency>) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 400));
      
      const currencyIndex = adminStore.currencies.findIndex(c => c.id === currencyId);
      if (currencyIndex === -1) {
        throw new Error('العملة غير موجودة');
      }

      const currency = adminStore.currencies[currencyIndex];
      
      // Prevent editing USD
      if (currency.code === 'USD') {
        throw new Error('لا يمكن تعديل الدولار الأمريكي');
      }

      // Update currency
      adminStore.currencies[currencyIndex] = {
        ...currency,
        ...currencyData,
        updatedAt: new Date().toISOString(),
        // Prevent changing code and default status
        code: currency.code,
        isDefault: currency.isDefault
      };
      
      return { success: true };
    } catch (error) {
      console.error('Failed to update currency:', error);
      throw error;
    }
  };

  const deleteCurrency = async (currencyId: number) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const currencyIndex = adminStore.currencies.findIndex(c => c.id === currencyId);
      if (currencyIndex === -1) {
        throw new Error('العملة غير موجودة');
      }

      const currency = adminStore.currencies[currencyIndex];
      
      // Prevent deleting USD
      if (currency.code === 'USD') {
        throw new Error('لا يمكن حذف الدولار الأمريكي');
      }

      // Check if currency is being used
      // In a real implementation, you would check if any products or transactions use this currency
      const isInUse = false; // Mock check
      if (isInUse) {
        throw new Error('لا يمكن حذف العملة لأنها مستخدمة في المنتجات أو المعاملات');
      }

      adminStore.currencies.splice(currencyIndex, 1);
      return { success: true };
    } catch (error) {
      console.error('Failed to delete currency:', error);
      throw error;
    }
  };

  const updateCurrencyStatus = async (currencyId: number, status: Currency['status']) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const currencyIndex = adminStore.currencies.findIndex(c => c.id === currencyId);
      if (currencyIndex === -1) {
        throw new Error('العملة غير موجودة');
      }

      const currency = adminStore.currencies[currencyIndex];
      
      // Prevent deactivating USD
      if (currency.code === 'USD' && status === 'inactive') {
        throw new Error('لا يمكن إلغاء تفعيل الدولار الأمريكي');
      }

      adminStore.currencies[currencyIndex].status = status;
      adminStore.currencies[currencyIndex].updatedAt = new Date().toISOString();
      
      return { success: true };
    } catch (error) {
      console.error('Failed to update currency status:', error);
      throw error;
    }
  };

  const updateExchangeRates = async (rates: Record<string, number>) => {
    try {
      // Mock API call to external exchange rate service
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      let updatedCount = 0;
      
      for (const [currencyCode, rate] of Object.entries(rates)) {
        const currencyIndex = adminStore.currencies.findIndex(c => c.code === currencyCode);
        if (currencyIndex !== -1 && currencyCode !== 'USD') {
          adminStore.currencies[currencyIndex].exchangeRate = rate;
          adminStore.currencies[currencyIndex].updatedAt = new Date().toISOString();
          updatedCount++;
        }
      }
      
      return { success: true, updatedCount };
    } catch (error) {
      console.error('Failed to update exchange rates:', error);
      throw error;
    }
  };

  const fetchLatestExchangeRates = async () => {
    try {
      // Mock API call to external service (e.g., exchangerate-api.com)
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock exchange rates
      const latestRates = {
        SAR: 3.75,
        EUR: 0.85,
        GBP: 0.73,
        AED: 3.67,
        EGP: 30.85,
        KWD: 0.31,
        QAR: 3.64,
        BHD: 0.38,
        OMR: 0.38,
        JOD: 0.71
      };
      
      return { success: true, rates: latestRates };
    } catch (error) {
      console.error('Failed to fetch latest exchange rates:', error);
      throw error;
    }
  };

  // ===== UTILITY FUNCTIONS =====
  const getStatusLabel = (status: Currency['status']): string => {
    const labels = {
      active: 'نشط',
      inactive: 'غير نشط'
    };
    return labels[status];
  };

  const getStatusColor = (status: Currency['status']): string => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-red-100 text-red-800'
    };
    return colors[status];
  };

  const formatExchangeRate = (rate: number): string => {
    return `1 USD = ${rate.toFixed(4)}`;
  };

  const convertFromUSD = (amountUSD: number, currencyCode: string): number => {
    return adminStore.convertFromUSD(amountUSD, currencyCode);
  };

  const convertToUSD = (amount: number, currencyCode: string): number => {
    return adminStore.convertToUSD(amount, currencyCode);
  };

  const formatCurrency = (amount: number, currencyCode: string): string => {
    return adminStore.formatCurrency(amount, currencyCode);
  };

  const getCurrencyByCode = (code: string): Currency | undefined => {
    return adminStore.currencies.find(c => c.code === code);
  };

  const validateCurrencyData = (data: Partial<Currency>): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!data.code || data.code.trim().length === 0) {
      errors.push('رمز العملة مطلوب');
    } else if (data.code.length !== 3) {
      errors.push('رمز العملة يجب أن يكون 3 أحرف');
    }

    if (!data.symbol || data.symbol.trim().length === 0) {
      errors.push('رمز العملة مطلوب');
    }

    if (!data.nameAr || data.nameAr.trim().length === 0) {
      errors.push('الاسم العربي مطلوب');
    }

    if (!data.nameEn || data.nameEn.trim().length === 0) {
      errors.push('الاسم الإنجليزي مطلوب');
    }

    if (!data.exchangeRate || data.exchangeRate <= 0) {
      errors.push('سعر الصرف يجب أن يكون أكبر من صفر');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const getPopularCurrencies = () => {
    return [
      { code: 'SAR', nameAr: 'ريال سعودي', nameEn: 'Saudi Riyal', symbol: 'ر.س' },
      { code: 'AED', nameAr: 'درهم إماراتي', nameEn: 'UAE Dirham', symbol: 'د.إ' },
      { code: 'EUR', nameAr: 'يورو', nameEn: 'Euro', symbol: '€' },
      { code: 'GBP', nameAr: 'جنيه إسترليني', nameEn: 'British Pound', symbol: '£' },
      { code: 'EGP', nameAr: 'جنيه مصري', nameEn: 'Egyptian Pound', symbol: 'ج.م' },
      { code: 'KWD', nameAr: 'دينار كويتي', nameEn: 'Kuwaiti Dinar', symbol: 'د.ك' },
      { code: 'QAR', nameAr: 'ريال قطري', nameEn: 'Qatari Riyal', symbol: 'ر.ق' },
      { code: 'BHD', nameAr: 'دينار بحريني', nameEn: 'Bahraini Dinar', symbol: 'د.ب' },
      { code: 'OMR', nameAr: 'ريال عماني', nameEn: 'Omani Rial', symbol: 'ر.ع' },
      { code: 'JOD', nameAr: 'دينار أردني', nameEn: 'Jordanian Dinar', symbol: 'د.أ' }
    ];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return {
    // State
    currencies,
    isLoading,
    activeCurrencies,
    defaultCurrency,
    
    // Actions
    fetchCurrencies,
    createCurrency,
    updateCurrency,
    deleteCurrency,
    updateCurrencyStatus,
    updateExchangeRates,
    fetchLatestExchangeRates,
    
    // Utilities
    getStatusLabel,
    getStatusColor,
    formatExchangeRate,
    convertFromUSD,
    convertToUSD,
    formatCurrency,
    getCurrencyByCode,
    validateCurrencyData,
    getPopularCurrencies,
    formatDate
  };
};
