<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <!-- Backdrop -->
    <div class="absolute inset-0 bg-black/60 backdrop-blur-sm" @click="$emit('close')"></div>

    <!-- Modal -->
    <div class="relative bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-6 w-full max-w-md">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <h2 class="section-title text-xl">إيداع رصيد</h2>
        <button @click="$emit('close')" class="text-theme-muted hover:text-white-force transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleDeposit" class="space-y-4">
        <!-- Amount -->
        <div>
          <label class="block text-white-force text-sm font-medium mb-2">المبلغ (ريال سعودي)</label>
          <input v-model="amount" type="number" min="10" max="10000" class="input-field" placeholder="أدخل المبلغ" required>
        </div>

        <!-- Payment Method -->
        <div>
          <label class="block text-white-force text-sm font-medium mb-2">طريقة الدفع</label>
          <select v-model="paymentMethod" class="input-field" required>
            <option value="">اختر طريقة الدفع</option>
            <option value="bank">تحويل بنكي</option>
            <option value="card">بطاقة ائتمان</option>
            <option value="stc">STC Pay</option>
            <option value="mada">مدى</option>
          </select>
        </div>

        <!-- Submit Button -->
        <button type="submit" class="btn-primary w-full py-3 mt-6">
          إيداع {{ amount ? Number(amount).toLocaleString() : '0' }} ر.س
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
defineEmits(['close'])

const amount = ref('')
const paymentMethod = ref('')

const handleDeposit = () => {
  console.log('Deposit:', { amount: amount.value, paymentMethod: paymentMethod.value })
  // Handle deposit logic here
}
</script>
