export default defineNuxtRouteMiddleware((to, from) => {
  const { isAuthenticated, canManageOrders } = useAuthStore();
  
  // First check if user is authenticated
  if (!isAuthenticated) {
    return navigateTo('/?login=required');
  }
  
  // Then check if user can manage orders (admin or worker)
  if (!canManageOrders) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Access denied. Worker or Admin privileges required.'
    });
  }
});
