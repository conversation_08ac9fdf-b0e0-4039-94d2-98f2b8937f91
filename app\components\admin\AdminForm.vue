<template>
  <form @submit.prevent="handleSubmit" class="admin-form">
    <div class="form-grid" :class="gridClasses">
      <div
        v-for="field in fields"
        :key="field.name"
        :class="[
          'form-field',
          field.span ? `col-span-${field.span}` : '',
          field.fullWidth ? 'col-span-full' : ''
        ]"
      >
        <!-- Field Label -->
        <label
          v-if="field.label"
          :for="field.name"
          class="form-label arabic-text"
          :class="{ 'required': field.required }"
        >
          {{ field.label }}
        </label>

        <!-- Text Input -->
        <input
          v-if="field.type === 'text' || field.type === 'email' || field.type === 'password' || field.type === 'number'"
          :id="field.name"
          :name="field.name"
          :type="field.type"
          :placeholder="field.placeholder"
          :required="field.required"
          :disabled="field.disabled || isLoading"
          :min="field.min"
          :max="field.max"
          :step="field.step"
          v-model="formData[field.name]"
          class="form-input arabic-text"
          :class="{ 'form-input-error': errors[field.name] }"
        />

        <!-- Textarea -->
        <textarea
          v-else-if="field.type === 'textarea'"
          :id="field.name"
          :name="field.name"
          :placeholder="field.placeholder"
          :required="field.required"
          :disabled="field.disabled || isLoading"
          :rows="field.rows || 3"
          v-model="formData[field.name]"
          class="form-textarea arabic-text"
          :class="{ 'form-input-error': errors[field.name] }"
        />

        <!-- Select -->
        <select
          v-else-if="field.type === 'select'"
          :id="field.name"
          :name="field.name"
          :required="field.required"
          :disabled="field.disabled || isLoading"
          v-model="formData[field.name]"
          class="form-select arabic-text"
          :class="{ 'form-input-error': errors[field.name] }"
        >
          <option value="" disabled>{{ field.placeholder || 'اختر...' }}</option>
          <option
            v-for="option in field.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </option>
        </select>

        <!-- Checkbox -->
        <label
          v-else-if="field.type === 'checkbox'"
          class="form-checkbox-label"
        >
          <input
            :id="field.name"
            :name="field.name"
            type="checkbox"
            :required="field.required"
            :disabled="field.disabled || isLoading"
            v-model="formData[field.name]"
            class="form-checkbox"
          />
          <span class="form-checkbox-text arabic-text">{{ field.checkboxLabel || field.label }}</span>
        </label>

        <!-- Radio Group -->
        <div v-else-if="field.type === 'radio'" class="form-radio-group">
          <label
            v-for="option in field.options"
            :key="option.value"
            class="form-radio-label"
          >
            <input
              :name="field.name"
              type="radio"
              :value="option.value"
              :required="field.required"
              :disabled="field.disabled || isLoading"
              v-model="formData[field.name]"
              class="form-radio"
            />
            <span class="form-radio-text arabic-text">{{ option.label }}</span>
          </label>
        </div>

        <!-- File Input -->
        <input
          v-else-if="field.type === 'file'"
          :id="field.name"
          :name="field.name"
          type="file"
          :accept="field.accept"
          :multiple="field.multiple"
          :required="field.required"
          :disabled="field.disabled || isLoading"
          @change="handleFileChange(field.name, $event)"
          class="form-file"
          :class="{ 'form-input-error': errors[field.name] }"
        />

        <!-- Custom Slot -->
        <slot
          v-else-if="field.type === 'custom'"
          :name="`field-${field.name}`"
          :field="field"
          :value="formData[field.name]"
          :error="errors[field.name]"
          :disabled="field.disabled || isLoading"
          @update="(value) => formData[field.name] = value"
        />

        <!-- Field Error -->
        <div v-if="errors[field.name]" class="form-error arabic-text">
          {{ errors[field.name] }}
        </div>

        <!-- Field Help Text -->
        <div v-if="field.help" class="form-help arabic-text">
          {{ field.help }}
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div v-if="showActions" class="form-actions">
      <slot name="actions">
        <div class="flex justify-end gap-3">
          <button
            v-if="showCancel"
            type="button"
            @click="$emit('cancel')"
            :disabled="isLoading"
            class="btn-secondary"
          >
            {{ cancelLabel }}
          </button>
          <button
            type="submit"
            :disabled="isLoading || !isValid"
            class="btn-primary"
          >
            <div v-if="isLoading" class="flex items-center gap-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>جاري الحفظ...</span>
            </div>
            <span v-else>{{ submitLabel }}</span>
          </button>
        </div>
      </slot>
    </div>
  </form>
</template>

<script setup lang="ts">
interface FormField {
  name: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file' | 'custom';
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  span?: number;
  fullWidth?: boolean;
  // Text/Number specific
  min?: number;
  max?: number;
  step?: number;
  // Textarea specific
  rows?: number;
  // Select/Radio specific
  options?: { value: any; label: string }[];
  // Checkbox specific
  checkboxLabel?: string;
  // File specific
  accept?: string;
  multiple?: boolean;
  // Help text
  help?: string;
}

interface Props {
  fields: FormField[];
  modelValue: Record<string, any>;
  errors?: Record<string, string>;
  isLoading?: boolean;
  columns?: number;
  showActions?: boolean;
  showCancel?: boolean;
  submitLabel?: string;
  cancelLabel?: string;
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => ({}),
  isLoading: false,
  columns: 1,
  showActions: true,
  showCancel: true,
  submitLabel: 'حفظ',
  cancelLabel: 'إلغاء'
});

const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  submit: [data: Record<string, any>];
  cancel: [];
  'file-change': [fieldName: string, files: FileList | null];
}>();

const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const gridClasses = computed(() => {
  const classes = ['grid', 'gap-4'];
  if (props.columns > 1) {
    classes.push(`grid-cols-1 md:grid-cols-${props.columns}`);
  }
  return classes.join(' ');
});

const isValid = computed(() => {
  // Check if all required fields are filled
  return props.fields.every(field => {
    if (!field.required) return true;
    const value = formData.value[field.name];
    return value !== undefined && value !== null && value !== '';
  });
});

const handleSubmit = () => {
  if (!isValid.value || props.isLoading) return;
  emit('submit', formData.value);
};

const handleFileChange = (fieldName: string, event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  
  // Update form data with file names or file objects
  if (files && files.length > 0) {
    formData.value[fieldName] = Array.from(files);
  } else {
    formData.value[fieldName] = null;
  }
  
  emit('file-change', fieldName, files);
};
</script>

<style scoped>
.admin-form {
  @apply space-y-8;
  animation: slideInUp 0.6s ease-out;
}

.form-grid {
  @apply grid gap-6;
}

.form-field {
  @apply space-y-3;
  animation: fadeInUp 0.4s ease-out;
  animation-fill-mode: both;
}

.form-field:nth-child(1) { animation-delay: 0.1s; }
.form-field:nth-child(2) { animation-delay: 0.2s; }
.form-field:nth-child(3) { animation-delay: 0.3s; }
.form-field:nth-child(4) { animation-delay: 0.4s; }
.form-field:nth-child(5) { animation-delay: 0.5s; }

.form-label {
  @apply block text-sm font-semibold;
  color: rgb(var(--color-text-primary));
  font-family: 'Cairo', sans-serif;
  letter-spacing: 0.5px;
}

.form-label.required::after {
  content: ' *';
  color: rgb(var(--color-error));
  font-weight: bold;
}

.form-input,
.form-textarea,
.form-select {
  @apply w-full px-4 py-3 rounded-xl transition-all duration-300;
  background: rgba(31, 41, 55, 0.5);
  border: 1px solid var(--glass-border);
  color: rgb(var(--color-text-primary));
  font-family: 'Cairo', sans-serif;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: rgb(var(--color-primary));
  box-shadow: 0 0 0 3px rgba(var(--color-primary), 0.1), 0 4px 16px rgba(var(--color-primary), 0.2);
  background: rgba(31, 41, 55, 0.7);
  transform: translateY(-2px);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: rgb(var(--color-text-muted));
  font-family: 'Cairo', sans-serif;
}

.form-input:disabled,
.form-textarea:disabled,
.form-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(31, 41, 55, 0.3);
}

.form-input-error {
  border-color: rgb(var(--color-error));
  box-shadow: 0 0 0 3px rgba(var(--color-error), 0.1);
  animation: shake 0.5s ease-in-out;
}

.form-input-error:focus {
  border-color: rgb(var(--color-error));
  box-shadow: 0 0 0 3px rgba(var(--color-error), 0.2);
}

.form-checkbox-label,
.form-radio-label {
  @apply flex items-center gap-3 cursor-pointer p-3 rounded-xl transition-all duration-300;
  background: rgba(147, 51, 234, 0.05);
  border: 1px solid var(--glass-border);
  font-family: 'Cairo', sans-serif;
}

.form-checkbox-label:hover,
.form-radio-label:hover {
  background: rgba(147, 51, 234, 0.1);
  transform: translateX(-2px);
  box-shadow: 0 4px 12px rgba(var(--color-primary), 0.1);
}

.form-checkbox,
.form-radio {
  @apply w-5 h-5 rounded transition-all duration-300;
  color: rgb(var(--color-primary));
  border: 2px solid var(--glass-border);
  background: rgba(31, 41, 55, 0.5);
}

.form-checkbox:checked,
.form-radio:checked {
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  border-color: rgb(var(--color-primary));
  box-shadow: 0 0 0 3px rgba(var(--color-primary), 0.2);
}

.form-checkbox:focus,
.form-radio:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--color-primary), 0.2);
}

.form-checkbox-text,
.form-radio-text {
  @apply text-sm font-medium;
  color: rgb(var(--color-text-primary));
  font-family: 'Cairo', sans-serif;
}

.form-radio-group {
  @apply space-y-3;
}

.form-file {
  @apply w-full text-sm rounded-xl transition-all duration-300;
  color: rgb(var(--color-text-primary));
  background: rgba(31, 41, 55, 0.5);
  border: 1px solid var(--glass-border);
  padding: 0.75rem;
  font-family: 'Cairo', sans-serif;
}

.form-file::file-selector-button {
  @apply mr-4 py-2 px-4 rounded-lg border-0 text-sm font-semibold text-white transition-all duration-300;
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  box-shadow: 0 2px 8px rgba(var(--color-primary), 0.3);
  font-family: 'Cairo', sans-serif;
}

.form-file::file-selector-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary), 0.4);
}

.form-error {
  @apply text-sm font-medium;
  color: rgb(var(--color-error));
  font-family: 'Cairo', sans-serif;
  animation: slideInDown 0.3s ease-out;
}

.form-help {
  @apply text-sm;
  color: rgb(var(--color-text-muted));
  font-family: 'Cairo', sans-serif;
}

.form-actions {
  @apply pt-6;
  border-top: 1px solid var(--glass-border);
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.02), rgba(59, 130, 246, 0.02));
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: var(--border-radius-xl);
}

.btn-primary {
  @apply font-semibold py-3 px-6 rounded-xl transition-all duration-300;
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  color: white;
  border: none;
  box-shadow: 0 4px 16px rgba(var(--color-primary), 0.3);
  font-family: 'Cairo', sans-serif;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--color-primary), 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  @apply font-semibold py-3 px-6 rounded-xl transition-all duration-300;
  background: rgba(147, 51, 234, 0.1);
  color: rgb(var(--color-primary));
  border: 1px solid var(--glass-border);
  font-family: 'Cairo', sans-serif;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(147, 51, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--color-primary), 0.2);
  border-color: rgb(var(--color-primary));
}

.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Mobile responsive */
@media (max-width: 640px) {
  .admin-form {
    @apply space-y-6;
  }

  .form-grid {
    @apply gap-4;
  }

  .form-field {
    @apply space-y-2;
  }

  .form-input,
  .form-textarea,
  .form-select {
    @apply py-2 px-3 text-sm;
  }

  .btn-primary,
  .btn-secondary {
    @apply py-2 px-4 text-sm;
  }

  .form-actions {
    @apply pt-4 p-4;
  }
}

/* RTL Support */
[dir="rtl"] .form-checkbox-label:hover,
[dir="rtl"] .form-radio-label:hover {
  transform: translateX(2px);
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .admin-form,
  .form-field,
  .form-input,
  .form-textarea,
  .form-select,
  .btn-primary,
  .btn-secondary {
    animation: none;
    transition: none;
  }

  .form-input:focus,
  .form-textarea:focus,
  .form-select:focus {
    transform: none;
  }
}
</style>
