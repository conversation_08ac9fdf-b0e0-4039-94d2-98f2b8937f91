<template>
  <form @submit.prevent="handleSubmit" class="admin-form">
    <div class="form-grid" :class="gridClasses">
      <div
        v-for="field in fields"
        :key="field.name"
        :class="[
          'form-field',
          field.span ? `col-span-${field.span}` : '',
          field.fullWidth ? 'col-span-full' : ''
        ]"
      >
        <!-- Field Label -->
        <label
          v-if="field.label"
          :for="field.name"
          class="form-label arabic-text"
          :class="{ 'required': field.required }"
        >
          {{ field.label }}
        </label>

        <!-- Text Input -->
        <input
          v-if="field.type === 'text' || field.type === 'email' || field.type === 'password' || field.type === 'number'"
          :id="field.name"
          :name="field.name"
          :type="field.type"
          :placeholder="field.placeholder"
          :required="field.required"
          :disabled="field.disabled || isLoading"
          :min="field.min"
          :max="field.max"
          :step="field.step"
          v-model="formData[field.name]"
          class="form-input arabic-text"
          :class="{ 'form-input-error': errors[field.name] }"
        />

        <!-- Textarea -->
        <textarea
          v-else-if="field.type === 'textarea'"
          :id="field.name"
          :name="field.name"
          :placeholder="field.placeholder"
          :required="field.required"
          :disabled="field.disabled || isLoading"
          :rows="field.rows || 3"
          v-model="formData[field.name]"
          class="form-textarea arabic-text"
          :class="{ 'form-input-error': errors[field.name] }"
        />

        <!-- Select -->
        <select
          v-else-if="field.type === 'select'"
          :id="field.name"
          :name="field.name"
          :required="field.required"
          :disabled="field.disabled || isLoading"
          v-model="formData[field.name]"
          class="form-select arabic-text"
          :class="{ 'form-input-error': errors[field.name] }"
        >
          <option value="" disabled>{{ field.placeholder || 'اختر...' }}</option>
          <option
            v-for="option in field.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </option>
        </select>

        <!-- Checkbox -->
        <label
          v-else-if="field.type === 'checkbox'"
          class="form-checkbox-label"
        >
          <input
            :id="field.name"
            :name="field.name"
            type="checkbox"
            :required="field.required"
            :disabled="field.disabled || isLoading"
            v-model="formData[field.name]"
            class="form-checkbox"
          />
          <span class="form-checkbox-text arabic-text">{{ field.checkboxLabel || field.label }}</span>
        </label>

        <!-- Radio Group -->
        <div v-else-if="field.type === 'radio'" class="form-radio-group">
          <label
            v-for="option in field.options"
            :key="option.value"
            class="form-radio-label"
          >
            <input
              :name="field.name"
              type="radio"
              :value="option.value"
              :required="field.required"
              :disabled="field.disabled || isLoading"
              v-model="formData[field.name]"
              class="form-radio"
            />
            <span class="form-radio-text arabic-text">{{ option.label }}</span>
          </label>
        </div>

        <!-- File Input -->
        <input
          v-else-if="field.type === 'file'"
          :id="field.name"
          :name="field.name"
          type="file"
          :accept="field.accept"
          :multiple="field.multiple"
          :required="field.required"
          :disabled="field.disabled || isLoading"
          @change="handleFileChange(field.name, $event)"
          class="form-file"
          :class="{ 'form-input-error': errors[field.name] }"
        />

        <!-- Custom Slot -->
        <slot
          v-else-if="field.type === 'custom'"
          :name="`field-${field.name}`"
          :field="field"
          :value="formData[field.name]"
          :error="errors[field.name]"
          :disabled="field.disabled || isLoading"
          @update="(value) => formData[field.name] = value"
        />

        <!-- Field Error -->
        <div v-if="errors[field.name]" class="form-error arabic-text">
          {{ errors[field.name] }}
        </div>

        <!-- Field Help Text -->
        <div v-if="field.help" class="form-help arabic-text">
          {{ field.help }}
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div v-if="showActions" class="form-actions">
      <slot name="actions">
        <div class="flex justify-end gap-3">
          <button
            v-if="showCancel"
            type="button"
            @click="$emit('cancel')"
            :disabled="isLoading"
            class="btn-secondary"
          >
            {{ cancelLabel }}
          </button>
          <button
            type="submit"
            :disabled="isLoading || !isValid"
            class="btn-primary"
          >
            <div v-if="isLoading" class="flex items-center gap-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>جاري الحفظ...</span>
            </div>
            <span v-else>{{ submitLabel }}</span>
          </button>
        </div>
      </slot>
    </div>
  </form>
</template>

<script setup lang="ts">
interface FormField {
  name: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file' | 'custom';
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  span?: number;
  fullWidth?: boolean;
  // Text/Number specific
  min?: number;
  max?: number;
  step?: number;
  // Textarea specific
  rows?: number;
  // Select/Radio specific
  options?: { value: any; label: string }[];
  // Checkbox specific
  checkboxLabel?: string;
  // File specific
  accept?: string;
  multiple?: boolean;
  // Help text
  help?: string;
}

interface Props {
  fields: FormField[];
  modelValue: Record<string, any>;
  errors?: Record<string, string>;
  isLoading?: boolean;
  columns?: number;
  showActions?: boolean;
  showCancel?: boolean;
  submitLabel?: string;
  cancelLabel?: string;
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => ({}),
  isLoading: false,
  columns: 1,
  showActions: true,
  showCancel: true,
  submitLabel: 'حفظ',
  cancelLabel: 'إلغاء'
});

const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  submit: [data: Record<string, any>];
  cancel: [];
  'file-change': [fieldName: string, files: FileList | null];
}>();

const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const gridClasses = computed(() => {
  const classes = ['grid', 'gap-4'];
  if (props.columns > 1) {
    classes.push(`grid-cols-1 md:grid-cols-${props.columns}`);
  }
  return classes.join(' ');
});

const isValid = computed(() => {
  // Check if all required fields are filled
  return props.fields.every(field => {
    if (!field.required) return true;
    const value = formData.value[field.name];
    return value !== undefined && value !== null && value !== '';
  });
});

const handleSubmit = () => {
  if (!isValid.value || props.isLoading) return;
  emit('submit', formData.value);
};

const handleFileChange = (fieldName: string, event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  
  // Update form data with file names or file objects
  if (files && files.length > 0) {
    formData.value[fieldName] = Array.from(files);
  } else {
    formData.value[fieldName] = null;
  }
  
  emit('file-change', fieldName, files);
};
</script>

<style scoped>
.admin-form {
  @apply space-y-6;
}

.form-grid {
  @apply grid gap-4;
}

.form-field {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-theme-primary;
}

.form-label.required::after {
  content: ' *';
  @apply text-red-500;
}

.form-input,
.form-textarea,
.form-select {
  @apply w-full px-3 py-2 border border-theme-border bg-theme-surface text-theme-primary rounded-lg focus:outline-none focus:ring-2 focus:ring-theme-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed;
}

.form-input-error {
  @apply border-red-500 focus:ring-red-500;
}

.form-checkbox-label,
.form-radio-label {
  @apply flex items-center gap-2 cursor-pointer;
}

.form-checkbox,
.form-radio {
  @apply text-theme-primary focus:ring-theme-primary border-theme-border;
}

.form-checkbox-text,
.form-radio-text {
  @apply text-sm text-theme-primary;
}

.form-radio-group {
  @apply space-y-2;
}

.form-file {
  @apply w-full text-sm text-theme-primary file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-theme-primary file:text-white hover:file:bg-theme-primary-hover;
}

.form-error {
  @apply text-sm text-red-600;
}

.form-help {
  @apply text-sm text-theme-muted;
}

.form-actions {
  @apply pt-4 border-t border-theme-border;
}

.btn-primary {
  @apply bg-theme-primary hover:bg-theme-primary-hover text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-theme-surface-light hover:bg-theme-surface text-theme-primary font-medium py-2 px-4 rounded-lg border border-theme-border transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>
