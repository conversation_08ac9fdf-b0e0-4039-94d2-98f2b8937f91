import type { AdminOrder } from '~/stores/admin';

export const useAdminOrders = () => {
  const adminStore = useAdminStore();
  
  // ===== REACTIVE STATE =====
  const orders = computed(() => adminStore.orders);
  const isLoading = computed(() => adminStore.isLoadingOrders);
  const filters = computed(() => adminStore.orderFilters);

  // ===== FILTERED ORDERS =====
  const filteredOrders = computed(() => {
    let filtered = [...adminStore.orders];
    
    // Status filter
    if (filters.value.status) {
      filtered = filtered.filter(order => order.status === filters.value.status);
    }
    
    // Date range filter
    if (filters.value.dateFrom) {
      const fromDate = new Date(filters.value.dateFrom);
      filtered = filtered.filter(order => new Date(order.createdAt) >= fromDate);
    }
    
    if (filters.value.dateTo) {
      const toDate = new Date(filters.value.dateTo);
      toDate.setHours(23, 59, 59, 999); // End of day
      filtered = filtered.filter(order => new Date(order.createdAt) <= toDate);
    }
    
    // Search filter
    if (filters.value.search) {
      const search = filters.value.search.toLowerCase();
      filtered = filtered.filter(order => 
        order.id.toString().includes(search) ||
        order.productName.toLowerCase().includes(search) ||
        order.productNameAr.toLowerCase().includes(search) ||
        order.userName.toLowerCase().includes(search) ||
        order.userEmail.toLowerCase().includes(search)
      );
    }
    
    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  });

  // ===== ACTIONS =====
  const fetchOrders = async () => {
    adminStore.isLoadingOrders = true;
    try {
      // Mock API call - replace with real implementation
      await new Promise(resolve => setTimeout(resolve, 700));
      
      // Mock data
      const mockOrders: AdminOrder[] = [
        {
          id: 1001,
          productId: 1,
          productName: 'Gaming Package Premium',
          productNameAr: 'باقة الألعاب المميزة',
          userId: 5,
          userName: 'خالد عبدالله',
          userEmail: '<EMAIL>',
          amountUSD: 49.99,
          displayCurrency: 'SAR',
          displayAmount: 187.50,
          status: 'completed',
          createdAt: '2024-01-30T14:20:00Z',
          updatedAt: '2024-01-30T15:45:00Z',
          workerId: 2,
          workerName: 'فاطمة علي',
          notes: 'تم التسليم بنجاح',
          costUSD: 35.00,
          profitUSD: 14.99
        },
        {
          id: 1002,
          productId: 2,
          productName: 'Steam Gift Card',
          productNameAr: 'بطاقة هدايا ستيم',
          userId: 6,
          userName: 'نورا حسن',
          userEmail: '<EMAIL>',
          amountUSD: 20.00,
          displayCurrency: 'USD',
          displayAmount: 20.00,
          status: 'pending',
          createdAt: '2024-01-30T16:30:00Z',
          updatedAt: '2024-01-30T16:30:00Z',
          costUSD: 18.00,
          profitUSD: 2.00
        },
        {
          id: 1003,
          productId: 3,
          productName: 'PlayStation Plus Subscription',
          productNameAr: 'اشتراك بلايستيشن بلس',
          userId: 5,
          userName: 'خالد عبدالله',
          userEmail: '<EMAIL>',
          amountUSD: 59.99,
          displayCurrency: 'SAR',
          displayAmount: 224.96,
          status: 'processing',
          createdAt: '2024-01-30T12:15:00Z',
          updatedAt: '2024-01-30T13:20:00Z',
          workerId: 3,
          workerName: 'محمد السعيد',
          notes: 'جاري المعالجة',
          costUSD: 45.00,
          profitUSD: 14.99
        },
        {
          id: 1004,
          productId: 5,
          productName: 'Mobile Game Currency',
          productNameAr: 'عملة الألعاب المحمولة',
          userId: 7,
          userName: 'عبدالرحمن محمد',
          userEmail: '<EMAIL>',
          amountUSD: 9.99,
          displayCurrency: 'EUR',
          displayAmount: 8.49,
          status: 'cancelled',
          createdAt: '2024-01-29T18:45:00Z',
          updatedAt: '2024-01-30T09:10:00Z',
          notes: 'ألغي بناء على طلب العميل',
          costUSD: 8.00,
          profitUSD: 0
        },
        {
          id: 1005,
          productId: 1,
          productName: 'Gaming Package Premium',
          productNameAr: 'باقة الألعاب المميزة',
          userId: 4,
          userName: 'سارة أحمد',
          userEmail: '<EMAIL>',
          amountUSD: 49.99,
          displayCurrency: 'USD',
          displayAmount: 49.99,
          status: 'refunded',
          createdAt: '2024-01-28T10:30:00Z',
          updatedAt: '2024-01-29T14:20:00Z',
          workerId: 2,
          workerName: 'فاطمة علي',
          notes: 'تم الاسترداد بسبب مشكلة تقنية',
          costUSD: 35.00,
          profitUSD: -49.99
        }
      ];
      
      adminStore.orders = mockOrders;
    } catch (error) {
      console.error('Failed to fetch orders:', error);
      throw error;
    } finally {
      adminStore.isLoadingOrders = false;
    }
  };

  const updateOrderStatus = async (orderId: number, status: AdminOrder['status'], notes?: string) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 400));
      
      const orderIndex = adminStore.orders.findIndex(o => o.id === orderId);
      if (orderIndex !== -1) {
        adminStore.orders[orderIndex].status = status;
        adminStore.orders[orderIndex].updatedAt = new Date().toISOString();
        if (notes) {
          adminStore.orders[orderIndex].notes = notes;
        }
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to update order status:', error);
      throw error;
    }
  };

  const assignWorker = async (orderId: number, workerId: number, workerName: string) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const orderIndex = adminStore.orders.findIndex(o => o.id === orderId);
      if (orderIndex !== -1) {
        adminStore.orders[orderIndex].workerId = workerId;
        adminStore.orders[orderIndex].workerName = workerName;
        adminStore.orders[orderIndex].updatedAt = new Date().toISOString();
        
        // Auto-update status to processing if it was pending
        if (adminStore.orders[orderIndex].status === 'pending') {
          adminStore.orders[orderIndex].status = 'processing';
        }
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to assign worker:', error);
      throw error;
    }
  };

  const addOrderNote = async (orderId: number, note: string) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 200));
      
      const orderIndex = adminStore.orders.findIndex(o => o.id === orderId);
      if (orderIndex !== -1) {
        const currentNotes = adminStore.orders[orderIndex].notes || '';
        const timestamp = new Date().toLocaleString('ar-SA');
        const newNote = currentNotes ? 
          `${currentNotes}\n[${timestamp}] ${note}` : 
          `[${timestamp}] ${note}`;
        
        adminStore.orders[orderIndex].notes = newNote;
        adminStore.orders[orderIndex].updatedAt = new Date().toISOString();
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to add order note:', error);
      throw error;
    }
  };

  // ===== FILTERS =====
  const setStatusFilter = (status: string) => {
    adminStore.orderFilters.status = status;
  };

  const setDateFromFilter = (date: string) => {
    adminStore.orderFilters.dateFrom = date;
  };

  const setDateToFilter = (date: string) => {
    adminStore.orderFilters.dateTo = date;
  };

  const setSearchFilter = (search: string) => {
    adminStore.orderFilters.search = search;
  };

  const clearFilters = () => {
    adminStore.orderFilters = {
      status: '',
      dateFrom: '',
      dateTo: '',
      search: ''
    };
  };

  // ===== STATISTICS =====
  const orderStats = computed(() => {
    const stats = {
      total: filteredOrders.value.length,
      pending: 0,
      processing: 0,
      completed: 0,
      cancelled: 0,
      refunded: 0,
      totalRevenueUSD: 0,
      totalProfitUSD: 0
    };

    filteredOrders.value.forEach(order => {
      stats[order.status]++;
      if (order.status === 'completed') {
        stats.totalRevenueUSD += order.amountUSD;
        stats.totalProfitUSD += order.profitUSD || 0;
      }
    });

    return stats;
  });

  // ===== UTILITY FUNCTIONS =====
  const getStatusLabel = (status: AdminOrder['status']): string => {
    const labels = {
      pending: 'في الانتظار',
      processing: 'قيد المعالجة',
      completed: 'مكتمل',
      cancelled: 'ملغي',
      refunded: 'مسترد'
    };
    return labels[status];
  };

  const getStatusColor = (status: AdminOrder['status']): string => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      processing: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
      refunded: 'bg-purple-100 text-purple-800'
    };
    return colors[status];
  };

  const formatAmount = (amountUSD: number, displayCurrency: string, displayAmount: number): string => {
    if (displayCurrency === 'USD') {
      return `$${amountUSD.toFixed(2)}`;
    }
    return adminStore.formatCurrency(displayAmount, displayCurrency);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateProfitMargin = (order: AdminOrder): number => {
    if (!order.profitUSD || order.amountUSD === 0) return 0;
    return (order.profitUSD / order.amountUSD) * 100;
  };

  return {
    // State
    orders,
    isLoading,
    filters,
    
    // Computed
    filteredOrders,
    orderStats,
    
    // Actions
    fetchOrders,
    updateOrderStatus,
    assignWorker,
    addOrderNote,
    
    // Filters
    setStatusFilter,
    setDateFromFilter,
    setDateToFilter,
    setSearchFilter,
    clearFilters,
    
    // Utilities
    getStatusLabel,
    getStatusColor,
    formatAmount,
    formatDate,
    calculateProfitMargin
  };
};
