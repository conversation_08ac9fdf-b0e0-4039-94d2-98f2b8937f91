// TODO: backend - Replace with real database integration
export default defineEventHandler(async (event) => {
  // Mock wallet transactions data
  const transactions = [
    {
      id: 1,
      userId: 4,
      type: "deposit",
      amount: 500.00,
      currency: "usd",
      description: "Bank transfer deposit",
      status: "completed",
      createdAt: "2025-07-20T10:30:00Z",
      completedAt: "2025-07-20T10:35:00Z"
    },
    {
      id: 2,
      userId: 4,
      type: "purchase",
      amount: -1599.99,
      currency: "usd",
      description: "Purchase: Premium Gaming Laptop (Pro)",
      status: "completed",
      orderId: 1,
      createdAt: "2025-07-25T14:30:00Z",
      completedAt: "2025-07-25T14:30:00Z"
    },
    {
      id: 3,
      userId: 4,
      type: "profit",
      amount: 299.99,
      currency: "usd",
      description: "Profit from order #1",
      status: "completed",
      orderId: 1,
      createdAt: "2025-07-25T15:45:00Z",
      completedAt: "2025-07-25T15:45:00Z"
    },
    {
      id: 4,
      userId: 4,
      type: "purchase",
      amount: -599.98,
      currency: "usd",
      description: "Purchase: Wireless Headphones (Standard) x2",
      status: "pending",
      orderId: 2,
      createdAt: "2025-07-28T10:15:00Z",
      completedAt: null
    },
    {
      id: 5,
      userId: 4,
      type: "deposit",
      amount: 1000.00,
      currency: "eur",
      description: "Cryptocurrency conversion",
      status: "completed",
      createdAt: "2025-07-15T16:20:00Z",
      completedAt: "2025-07-15T16:25:00Z"
    },
    {
      id: 6,
      userId: 4,
      type: "purchase",
      amount: -199.99,
      currency: "usd",
      description: "Purchase: Running Shoes (Pro)",
      status: "completed",
      orderId: 4,
      createdAt: "2025-07-20T09:45:00Z",
      completedAt: "2025-07-20T09:45:00Z"
    },
    {
      id: 7,
      userId: 4,
      type: "profit",
      amount: 49.99,
      currency: "usd",
      description: "Profit from order #4",
      status: "completed",
      orderId: 4,
      createdAt: "2025-07-21T11:30:00Z",
      completedAt: "2025-07-21T11:30:00Z"
    },
    {
      id: 8,
      userId: 4,
      type: "deposit",
      amount: 0.05,
      currency: "btc",
      description: "Bitcoin deposit",
      status: "completed",
      createdAt: "2025-07-10T12:00:00Z",
      completedAt: "2025-07-10T12:15:00Z"
    }
  ];

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 90));

  return {
    success: true,
    data: transactions,
    total: transactions.length
  };
});
