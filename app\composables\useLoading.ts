/**
 * Loading state management composable
 */

interface LoadingState {
  [key: string]: boolean;
}

const globalLoadingState = ref<LoadingState>({});

export const useLoading = (namespace?: string) => {
  const localLoadingState = ref<LoadingState>({});

  /**
   * Get the appropriate loading state (global or local)
   */
  const getLoadingState = () => {
    return namespace ? globalLoadingState.value : localLoadingState.value;
  };

  /**
   * Set loading state for a specific key
   */
  const setLoading = (key: string, loading: boolean) => {
    const state = getLoadingState();
    if (loading) {
      state[key] = true;
    } else {
      delete state[key];
    }
  };

  /**
   * Check if a specific key is loading
   */
  const isLoading = (key: string): boolean => {
    const state = getLoadingState();
    return !!state[key];
  };

  /**
   * Check if any operation is loading
   */
  const isAnyLoading = computed((): boolean => {
    const state = getLoadingState();
    return Object.keys(state).length > 0;
  });

  /**
   * Get all currently loading keys
   */
  const loadingKeys = computed((): string[] => {
    const state = getLoadingState();
    return Object.keys(state);
  });

  /**
   * Clear all loading states
   */
  const clearAll = () => {
    const state = getLoadingState();
    Object.keys(state).forEach(key => delete state[key]);
  };

  /**
   * Wrap an async function with loading state
   */
  const withLoading = async <T>(
    key: string,
    asyncFn: () => Promise<T>
  ): Promise<T> => {
    setLoading(key, true);
    try {
      const result = await asyncFn();
      return result;
    } finally {
      setLoading(key, false);
    }
  };

  /**
   * Create a loading wrapper for multiple operations
   */
  const createLoadingWrapper = (key: string) => {
    return {
      isLoading: computed(() => isLoading(key)),
      setLoading: (loading: boolean) => setLoading(key, loading),
      withLoading: <T>(asyncFn: () => Promise<T>) => withLoading(key, asyncFn)
    };
  };

  return {
    setLoading,
    isLoading,
    isAnyLoading,
    loadingKeys,
    clearAll,
    withLoading,
    createLoadingWrapper
  };
};

/**
 * Global loading state helpers
 */
export const useGlobalLoading = () => {
  return useLoading('global');
};

/**
 * API loading states composable
 */
export const useApiLoading = () => {
  const loading = useLoading();

  const apiStates = {
    // Products
    fetchingProducts: computed(() => loading.isLoading('products:fetch')),
    creatingProduct: computed(() => loading.isLoading('products:create')),
    updatingProduct: computed(() => loading.isLoading('products:update')),
    deletingProduct: computed(() => loading.isLoading('products:delete')),

    // Orders
    fetchingOrders: computed(() => loading.isLoading('orders:fetch')),
    creatingOrder: computed(() => loading.isLoading('orders:create')),
    updatingOrder: computed(() => loading.isLoading('orders:update')),
    cancellingOrder: computed(() => loading.isLoading('orders:cancel')),

    // Users
    fetchingUsers: computed(() => loading.isLoading('users:fetch')),
    creatingUser: computed(() => loading.isLoading('users:create')),
    updatingUser: computed(() => loading.isLoading('users:update')),
    deletingUser: computed(() => loading.isLoading('users:delete')),

    // Categories
    fetchingCategories: computed(() => loading.isLoading('categories:fetch')),
    creatingCategory: computed(() => loading.isLoading('categories:create')),
    updatingCategory: computed(() => loading.isLoading('categories:update')),
    deletingCategory: computed(() => loading.isLoading('categories:delete')),

    // Wallet
    fetchingBalance: computed(() => loading.isLoading('wallet:balance')),
    fetchingTransactions: computed(() => loading.isLoading('wallet:transactions')),
    processing: computed(() => loading.isLoading('wallet:deposit') || loading.isLoading('wallet:withdraw')),
    depositing: computed(() => loading.isLoading('wallet:deposit')),
    withdrawing: computed(() => loading.isLoading('wallet:withdraw')),

    // Authentication
    loggingIn: computed(() => loading.isLoading('auth:login')),
    loggingOut: computed(() => loading.isLoading('auth:logout')),
    registering: computed(() => loading.isLoading('auth:register'))
  };

  const apiActions = {
    // Products
    withProductsFetch: (fn: () => Promise<any>) => loading.withLoading('products:fetch', fn),
    withProductCreate: (fn: () => Promise<any>) => loading.withLoading('products:create', fn),
    withProductUpdate: (fn: () => Promise<any>) => loading.withLoading('products:update', fn),
    withProductDelete: (fn: () => Promise<any>) => loading.withLoading('products:delete', fn),

    // Orders
    withOrdersFetch: (fn: () => Promise<any>) => loading.withLoading('orders:fetch', fn),
    withOrderCreate: (fn: () => Promise<any>) => loading.withLoading('orders:create', fn),
    withOrderUpdate: (fn: () => Promise<any>) => loading.withLoading('orders:update', fn),
    withOrderCancel: (fn: () => Promise<any>) => loading.withLoading('orders:cancel', fn),

    // Users
    withUsersFetch: (fn: () => Promise<any>) => loading.withLoading('users:fetch', fn),
    withUserCreate: (fn: () => Promise<any>) => loading.withLoading('users:create', fn),
    withUserUpdate: (fn: () => Promise<any>) => loading.withLoading('users:update', fn),
    withUserDelete: (fn: () => Promise<any>) => loading.withLoading('users:delete', fn),

    // Categories
    withCategoriesFetch: (fn: () => Promise<any>) => loading.withLoading('categories:fetch', fn),
    withCategoryCreate: (fn: () => Promise<any>) => loading.withLoading('categories:create', fn),
    withCategoryUpdate: (fn: () => Promise<any>) => loading.withLoading('categories:update', fn),
    withCategoryDelete: (fn: () => Promise<any>) => loading.withLoading('categories:delete', fn),

    // Wallet
    withBalanceFetch: (fn: () => Promise<any>) => loading.withLoading('wallet:balance', fn),
    withTransactionsFetch: (fn: () => Promise<any>) => loading.withLoading('wallet:transactions', fn),
    withDeposit: (fn: () => Promise<any>) => loading.withLoading('wallet:deposit', fn),
    withWithdraw: (fn: () => Promise<any>) => loading.withLoading('wallet:withdraw', fn),

    // Authentication
    withLogin: (fn: () => Promise<any>) => loading.withLoading('auth:login', fn),
    withLogout: (fn: () => Promise<any>) => loading.withLoading('auth:logout', fn),
    withRegister: (fn: () => Promise<any>) => loading.withLoading('auth:register', fn)
  };

  return {
    ...apiStates,
    ...apiActions,
    isAnyApiLoading: loading.isAnyLoading,
    clearAllApiLoading: loading.clearAll
  };
};
