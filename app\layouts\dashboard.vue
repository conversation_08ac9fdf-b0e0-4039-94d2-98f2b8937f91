<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Top Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo & Dashboard Title -->
          <div class="flex items-center space-x-4">
            <NuxtLink to="/" class="text-xl font-bold text-gray-900">
              E-Store
            </NuxtLink>
            <span class="text-gray-400">|</span>
            <h1 class="text-lg font-medium text-gray-700">Admin Dashboard</h1>
          </div>

          <!-- User Actions -->
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-600">{{ userDisplayName }}</span>
              <span :class="roleClass" class="badge">{{ userRole }}</span>
            </div>
            
            <NuxtLink to="/" class="btn-secondary text-sm">
              Back to Store
            </NuxtLink>
            
            <button 
              @click="handleLogout"
              class="btn-secondary text-sm"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="flex">
      <!-- Sidebar -->
      <aside class="w-64 bg-white shadow-sm min-h-screen">
        <nav class="mt-8">
          <div class="px-4">
            <h2 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-4">
              Dashboard
            </h2>
            
            <div class="space-y-1">
              <!-- Overview -->
              <NuxtLink 
                to="/admin" 
                class="dashboard-nav-link"
                :class="{ 'dashboard-nav-active': $route.path === '/admin' }"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Overview
              </NuxtLink>

              <!-- Users Management -->
              <NuxtLink 
                v-if="canAccessAdmin"
                to="/admin/users" 
                class="dashboard-nav-link"
                :class="{ 'dashboard-nav-active': $route.path.startsWith('/admin/users') }"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                Users
              </NuxtLink>

              <!-- Products Management -->
              <NuxtLink 
                to="/admin/products" 
                class="dashboard-nav-link"
                :class="{ 'dashboard-nav-active': $route.path.startsWith('/admin/products') }"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                Products
              </NuxtLink>

              <!-- Categories Management -->
              <NuxtLink 
                v-if="canAccessAdmin"
                to="/admin/categories" 
                class="dashboard-nav-link"
                :class="{ 'dashboard-nav-active': $route.path.startsWith('/admin/categories') }"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                Categories
              </NuxtLink>

              <!-- Orders Management -->
              <NuxtLink 
                to="/admin/orders" 
                class="dashboard-nav-link"
                :class="{ 'dashboard-nav-active': $route.path.startsWith('/admin/orders') }"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Orders
              </NuxtLink>

              <!-- Homepage Builder -->
              <NuxtLink 
                v-if="canAccessAdmin"
                to="/admin/homepage" 
                class="dashboard-nav-link"
                :class="{ 'dashboard-nav-active': $route.path.startsWith('/admin/homepage') }"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                </svg>
                Homepage
              </NuxtLink>
            </div>
          </div>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="flex-1 p-8">
        <slot />
      </main>
    </div>

    <!-- Notifications -->
    <NotificationContainer />
  </div>
</template>

<script setup lang="ts">
const { userDisplayName, userRole, canAccessAdmin, logout } = useAuthStore();

const roleClass = computed(() => {
  const roleClasses = {
    admin: 'badge-admin',
    worker: 'badge-worker', 
    distributor: 'badge-distributor',
    user: 'badge-user'
  };
  return roleClasses[userRole as keyof typeof roleClasses] || 'badge-user';
});

const handleLogout = async () => {
  await logout();
};
</script>

<style scoped>
.dashboard-nav-link {
  @apply flex items-center space-x-3 px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 transition-colors;
}

.dashboard-nav-active {
  @apply bg-blue-50 text-blue-700 border-r-2 border-blue-600;
}
</style>
