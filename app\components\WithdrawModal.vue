<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <!-- Backdrop -->
    <div class="absolute inset-0 bg-black/60 backdrop-blur-sm" @click="$emit('close')"></div>

    <!-- Modal -->
    <div class="relative bg-theme-glass backdrop-blur-md border border-theme-light rounded-2xl p-6 w-full max-w-md">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <h2 class="section-title text-xl">سحب رصيد</h2>
        <button @click="$emit('close')" class="text-theme-muted hover:text-white-force transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form @submit.prevent="handleWithdraw" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Currency</label>
          <select v-model="currency" class="input-field" @change="updateMaxAmount">
            <option value="usd">USD ($)</option>
            <option value="eur">EUR (€)</option>
            <option value="btc">BTC (₿)</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Amount (Available: {{ formatAvailable() }})
          </label>
          <input 
            v-model.number="amount" 
            type="number" 
            :step="currency === 'btc' ? '0.000001' : '0.01'"
            :min="currency === 'btc' ? '0.000001' : '0.01'"
            :max="maxAmount"
            required 
            class="input-field"
            :placeholder="`Enter amount in ${currency.toUpperCase()}`"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Withdrawal Method</label>
          <select v-model="withdrawalMethod" class="input-field">
            <option value="bank">Bank Transfer</option>
            <option value="card">Credit/Debit Card</option>
            <option value="crypto">Cryptocurrency Wallet</option>
            <option value="paypal">PayPal</option>
          </select>
        </div>

        <div v-if="error" class="text-red-600 text-sm">
          {{ error }}
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <p class="text-sm text-yellow-800">
            <strong>Demo Mode:</strong> This is a mock withdrawal. No real funds will be transferred.
          </p>
        </div>

        <button 
          type="submit" 
          :disabled="isLoading || !canWithdraw"
          class="w-full"
          :class="canWithdraw ? 'btn-primary' : 'btn-secondary opacity-50 cursor-not-allowed'"
        >
          {{ isLoading ? 'Processing...' : `Withdraw ${formatAmount()}` }}
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['close', 'success']);

const { balance, withdraw } = useWalletStore();

const currency = ref('usd');
const amount = ref(0);
const withdrawalMethod = ref('bank');
const error = ref('');
const isLoading = ref(false);
const maxAmount = ref(0);

const updateMaxAmount = () => {
  maxAmount.value = balance[currency.value as keyof typeof balance] || 0;
  if (amount.value > maxAmount.value) {
    amount.value = maxAmount.value;
  }
};

const formatAvailable = () => {
  const available = balance[currency.value as keyof typeof balance] || 0;
  const symbols = { usd: '$', eur: '€', btc: '₿' };
  const symbol = symbols[currency.value as keyof typeof symbols];
  
  if (currency.value === 'btc') {
    return `${symbol}${available.toFixed(6)}`;
  }
  return `${symbol}${available.toFixed(2)}`;
};

const formatAmount = () => {
  if (!amount.value) return '';
  
  const symbols = { usd: '$', eur: '€', btc: '₿' };
  const symbol = symbols[currency.value as keyof typeof symbols];
  
  if (currency.value === 'btc') {
    return `${symbol}${amount.value.toFixed(6)}`;
  }
  return `${symbol}${amount.value.toFixed(2)}`;
};

const canWithdraw = computed(() => {
  return amount.value > 0 && amount.value <= maxAmount.value;
});

const handleWithdraw = async () => {
  error.value = '';
  
  if (!canWithdraw.value) {
    error.value = 'Invalid withdrawal amount';
    return;
  }

  isLoading.value = true;

  try {
    // TODO: backend - Replace with real withdrawal processing
    const result = await withdraw(amount.value, currency.value);
    
    if (result.success) {
      emit('success');
    } else {
      error.value = 'Withdrawal failed. Please try again.';
    }
  } catch (err) {
    error.value = 'An error occurred. Please try again.';
  } finally {
    isLoading.value = false;
  }
};

// Initialize max amount on mount
onMounted(() => {
  updateMaxAmount();
});

// Watch currency changes
watch(currency, updateMaxAmount);
</script>
