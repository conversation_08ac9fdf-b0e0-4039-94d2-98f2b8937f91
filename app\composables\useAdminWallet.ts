import type { WalletTransaction } from '~/stores/admin';

export const useAdminWallet = () => {
  const adminStore = useAdminStore();
  
  // ===== REACTIVE STATE =====
  const transactions = computed(() => adminStore.walletTransactions);
  const isLoading = computed(() => adminStore.isLoadingWallet);
  const filters = computed(() => adminStore.walletFilters);

  // ===== FILTERED TRANSACTIONS =====
  const filteredTransactions = computed(() => {
    let filtered = [...adminStore.walletTransactions];
    
    // Type filter
    if (filters.value.type) {
      filtered = filtered.filter(tx => tx.type === filters.value.type);
    }
    
    // Status filter
    if (filters.value.status) {
      filtered = filtered.filter(tx => tx.status === filters.value.status);
    }
    
    // Currency filter
    if (filters.value.currency) {
      filtered = filtered.filter(tx => tx.displayCurrency === filters.value.currency);
    }
    
    // Date range filter
    if (filters.value.dateFrom) {
      const fromDate = new Date(filters.value.dateFrom);
      filtered = filtered.filter(tx => new Date(tx.createdAt) >= fromDate);
    }
    
    if (filters.value.dateTo) {
      const toDate = new Date(filters.value.dateTo);
      toDate.setHours(23, 59, 59, 999); // End of day
      filtered = filtered.filter(tx => new Date(tx.createdAt) <= toDate);
    }
    
    // Search filter
    if (filters.value.search) {
      const search = filters.value.search.toLowerCase();
      filtered = filtered.filter(tx => 
        tx.id.toString().includes(search) ||
        tx.userName.toLowerCase().includes(search) ||
        tx.userEmail.toLowerCase().includes(search) ||
        (tx.reference && tx.reference.toLowerCase().includes(search))
      );
    }
    
    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  });

  // ===== ACTIONS =====
  const fetchTransactions = async () => {
    adminStore.isLoadingWallet = true;
    try {
      // Mock API call - replace with real implementation
      await new Promise(resolve => setTimeout(resolve, 600));
      
      // Mock data
      const mockTransactions: WalletTransaction[] = [
        {
          id: 2001,
          userId: 5,
          userName: 'خالد عبدالله',
          userEmail: '<EMAIL>',
          type: 'deposit',
          amountUSD: 100.00,
          displayCurrency: 'SAR',
          displayAmount: 375.00,
          status: 'completed',
          createdAt: '2024-01-30T10:15:00Z',
          updatedAt: '2024-01-30T10:20:00Z',
          method: 'bank_transfer',
          reference: 'TXN-SAR-001',
          notes: 'تحويل بنكي من البنك الأهلي'
        },
        {
          id: 2002,
          userId: 6,
          userName: 'نورا حسن',
          userEmail: '<EMAIL>',
          type: 'deposit',
          amountUSD: 50.00,
          displayCurrency: 'USD',
          displayAmount: 50.00,
          status: 'pending',
          createdAt: '2024-01-30T14:30:00Z',
          updatedAt: '2024-01-30T14:30:00Z',
          method: 'credit_card',
          reference: 'TXN-USD-002',
          notes: 'إيداع بالبطاقة الائتمانية'
        },
        {
          id: 2003,
          userId: 4,
          userName: 'سارة أحمد',
          userEmail: '<EMAIL>',
          type: 'withdrawal',
          amountUSD: 200.00,
          displayCurrency: 'SAR',
          displayAmount: 750.00,
          status: 'completed',
          createdAt: '2024-01-29T16:45:00Z',
          updatedAt: '2024-01-30T09:15:00Z',
          method: 'bank_transfer',
          reference: 'TXN-SAR-003',
          notes: 'سحب إلى حساب الراجحي'
        },
        {
          id: 2004,
          userId: 7,
          userName: 'عبدالرحمن محمد',
          userEmail: '<EMAIL>',
          type: 'refund',
          amountUSD: 49.99,
          displayCurrency: 'USD',
          displayAmount: 49.99,
          status: 'completed',
          createdAt: '2024-01-29T14:20:00Z',
          updatedAt: '2024-01-29T14:25:00Z',
          method: 'wallet_credit',
          reference: 'REF-USD-004',
          notes: 'استرداد طلب #1005'
        },
        {
          id: 2005,
          userId: 5,
          userName: 'خالد عبدالله',
          userEmail: '<EMAIL>',
          type: 'bonus',
          amountUSD: 25.00,
          displayCurrency: 'SAR',
          displayAmount: 93.75,
          status: 'completed',
          createdAt: '2024-01-28T12:00:00Z',
          updatedAt: '2024-01-28T12:00:00Z',
          method: 'system_credit',
          reference: 'BONUS-001',
          notes: 'مكافأة العضو الجديد'
        },
        {
          id: 2006,
          userId: 6,
          userName: 'نورا حسن',
          userEmail: '<EMAIL>',
          type: 'withdrawal',
          amountUSD: 30.00,
          displayCurrency: 'EUR',
          displayAmount: 25.50,
          status: 'failed',
          createdAt: '2024-01-27T18:30:00Z',
          updatedAt: '2024-01-28T08:15:00Z',
          method: 'paypal',
          reference: 'TXN-EUR-006',
          notes: 'فشل في المعالجة - حساب PayPal غير صحيح'
        }
      ];
      
      adminStore.walletTransactions = mockTransactions;
    } catch (error) {
      console.error('Failed to fetch wallet transactions:', error);
      throw error;
    } finally {
      adminStore.isLoadingWallet = false;
    }
  };

  const updateTransactionStatus = async (transactionId: number, status: WalletTransaction['status'], notes?: string) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 400));
      
      const txIndex = adminStore.walletTransactions.findIndex(tx => tx.id === transactionId);
      if (txIndex !== -1) {
        adminStore.walletTransactions[txIndex].status = status;
        adminStore.walletTransactions[txIndex].updatedAt = new Date().toISOString();
        if (notes) {
          adminStore.walletTransactions[txIndex].notes = notes;
        }
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to update transaction status:', error);
      throw error;
    }
  };

  const exportTransactions = async (format: 'csv' | 'excel' = 'csv') => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const data = filteredTransactions.value.map(tx => ({
        'رقم المعاملة': tx.id,
        'اسم المستخدم': tx.userName,
        'البريد الإلكتروني': tx.userEmail,
        'نوع المعاملة': getTypeLabel(tx.type),
        'المبلغ (دولار)': tx.amountUSD,
        'العملة المعروضة': tx.displayCurrency,
        'المبلغ المعروض': tx.displayAmount,
        'الحالة': getStatusLabel(tx.status),
        'الطريقة': tx.method || '-',
        'المرجع': tx.reference || '-',
        'تاريخ الإنشاء': formatDate(tx.createdAt),
        'ملاحظات': tx.notes || '-'
      }));
      
      // In a real implementation, this would generate and download the file
      console.log('Exporting transactions:', data);
      
      return { success: true, data };
    } catch (error) {
      console.error('Failed to export transactions:', error);
      throw error;
    }
  };

  // ===== FILTERS =====
  const setTypeFilter = (type: string) => {
    adminStore.walletFilters.type = type;
  };

  const setStatusFilter = (status: string) => {
    adminStore.walletFilters.status = status;
  };

  const setCurrencyFilter = (currency: string) => {
    adminStore.walletFilters.currency = currency;
  };

  const setDateFromFilter = (date: string) => {
    adminStore.walletFilters.dateFrom = date;
  };

  const setDateToFilter = (date: string) => {
    adminStore.walletFilters.dateTo = date;
  };

  const setSearchFilter = (search: string) => {
    adminStore.walletFilters.search = search;
  };

  const clearFilters = () => {
    adminStore.walletFilters = {
      type: '',
      status: '',
      currency: '',
      dateFrom: '',
      dateTo: '',
      search: ''
    };
  };

  // ===== STATISTICS =====
  const walletStats = computed(() => {
    const stats = {
      totalTransactions: filteredTransactions.value.length,
      totalDepositsUSD: 0,
      totalWithdrawalsUSD: 0,
      totalRefundsUSD: 0,
      totalBonusesUSD: 0,
      pendingTransactions: 0,
      completedTransactions: 0,
      failedTransactions: 0,
      netBalanceUSD: 0
    };

    filteredTransactions.value.forEach(tx => {
      if (tx.status === 'completed') {
        switch (tx.type) {
          case 'deposit':
            stats.totalDepositsUSD += tx.amountUSD;
            break;
          case 'withdrawal':
            stats.totalWithdrawalsUSD += tx.amountUSD;
            break;
          case 'refund':
            stats.totalRefundsUSD += tx.amountUSD;
            break;
          case 'bonus':
            stats.totalBonusesUSD += tx.amountUSD;
            break;
        }
      }

      switch (tx.status) {
        case 'pending':
          stats.pendingTransactions++;
          break;
        case 'completed':
          stats.completedTransactions++;
          break;
        case 'failed':
        case 'cancelled':
          stats.failedTransactions++;
          break;
      }
    });

    stats.netBalanceUSD = stats.totalDepositsUSD + stats.totalBonusesUSD - stats.totalWithdrawalsUSD;

    return stats;
  });

  // ===== UTILITY FUNCTIONS =====
  const getTypeLabel = (type: WalletTransaction['type']): string => {
    const labels = {
      deposit: 'إيداع',
      withdrawal: 'سحب',
      refund: 'استرداد',
      bonus: 'مكافأة'
    };
    return labels[type];
  };

  const getStatusLabel = (status: WalletTransaction['status']): string => {
    const labels = {
      pending: 'في الانتظار',
      completed: 'مكتمل',
      failed: 'فشل',
      cancelled: 'ملغي'
    };
    return labels[status];
  };

  const getStatusColor = (status: WalletTransaction['status']): string => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800'
    };
    return colors[status];
  };

  const getTypeColor = (type: WalletTransaction['type']): string => {
    const colors = {
      deposit: 'text-green-600',
      withdrawal: 'text-red-600',
      refund: 'text-blue-600',
      bonus: 'text-purple-600'
    };
    return colors[type];
  };

  const formatAmount = (amountUSD: number, displayCurrency: string, displayAmount: number): string => {
    if (displayCurrency === 'USD') {
      return `$${amountUSD.toFixed(2)}`;
    }
    return adminStore.formatCurrency(displayAmount, displayCurrency);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return {
    // State
    transactions,
    isLoading,
    filters,
    
    // Computed
    filteredTransactions,
    walletStats,
    
    // Actions
    fetchTransactions,
    updateTransactionStatus,
    exportTransactions,
    
    // Filters
    setTypeFilter,
    setStatusFilter,
    setCurrencyFilter,
    setDateFromFilter,
    setDateToFilter,
    setSearchFilter,
    clearFilters,
    
    // Utilities
    getTypeLabel,
    getStatusLabel,
    getStatusColor,
    getTypeColor,
    formatAmount,
    formatDate
  };
};
