<template>
  <div class="filter-bar">
    <div class="filter-grid">
      <!-- Filter Fields -->
      <div
        v-for="filter in filters"
        :key="filter.name"
        class="filter-field"
      >
        <label v-if="filter.label" class="filter-label arabic-text">
          {{ filter.label }}
        </label>

        <!-- Select Filter -->
        <select
          v-if="filter.type === 'select'"
          :value="modelValue[filter.name]"
          @change="updateFilter(filter.name, ($event.target as HTMLSelectElement).value)"
          class="filter-select arabic-text"
        >
          <option value="">{{ filter.placeholder || 'الكل' }}</option>
          <option
            v-for="option in filter.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </option>
        </select>

        <!-- Date Filter -->
        <input
          v-else-if="filter.type === 'date'"
          type="date"
          :value="modelValue[filter.name]"
          @input="updateFilter(filter.name, ($event.target as HTMLInputElement).value)"
          class="filter-input"
        />

        <!-- Text Filter -->
        <input
          v-else-if="filter.type === 'text'"
          type="text"
          :placeholder="filter.placeholder"
          :value="modelValue[filter.name]"
          @input="updateFilter(filter.name, ($event.target as HTMLInputElement).value)"
          class="filter-input arabic-text"
        />

        <!-- Number Filter -->
        <input
          v-else-if="filter.type === 'number'"
          type="number"
          :placeholder="filter.placeholder"
          :min="filter.min"
          :max="filter.max"
          :step="filter.step"
          :value="modelValue[filter.name]"
          @input="updateFilter(filter.name, ($event.target as HTMLInputElement).value)"
          class="filter-input"
        />

        <!-- Custom Filter Slot -->
        <slot
          v-else-if="filter.type === 'custom'"
          :name="`filter-${filter.name}`"
          :filter="filter"
          :value="modelValue[filter.name]"
          @update="(value) => updateFilter(filter.name, value)"
        />
      </div>

      <!-- Clear Filters Button -->
      <div class="filter-actions">
        <button
          @click="clearFilters"
          :disabled="!hasActiveFilters"
          class="btn-clear"
          title="مسح الفلاتر"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
          <span class="hidden sm:inline arabic-text">مسح</span>
        </button>
      </div>
    </div>

    <!-- Active Filters Display -->
    <div v-if="hasActiveFilters && showActiveFilters" class="active-filters">
      <div class="flex items-center gap-2 flex-wrap">
        <span class="text-sm text-theme-secondary arabic-text">الفلاتر النشطة:</span>
        <div
          v-for="(value, key) in activeFilters"
          :key="key"
          class="active-filter-tag"
        >
          <span class="arabic-text">{{ getFilterLabel(key) }}: {{ getFilterValueLabel(key, value) }}</span>
          <button
            @click="updateFilter(key, '')"
            class="active-filter-remove"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface FilterOption {
  value: any;
  label: string;
}

interface Filter {
  name: string;
  type: 'select' | 'date' | 'text' | 'number' | 'custom';
  label?: string;
  placeholder?: string;
  options?: FilterOption[];
  min?: number;
  max?: number;
  step?: number;
}

interface Props {
  filters: Filter[];
  modelValue: Record<string, any>;
  showActiveFilters?: boolean;
  columns?: number;
}

const props = withDefaults(defineProps<Props>(), {
  showActiveFilters: true,
  columns: 4
});

const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  clear: [];
}>();

const activeFilters = computed(() => {
  const active: Record<string, any> = {};
  for (const [key, value] of Object.entries(props.modelValue)) {
    if (value !== '' && value !== null && value !== undefined) {
      active[key] = value;
    }
  }
  return active;
});

const hasActiveFilters = computed(() => {
  return Object.keys(activeFilters.value).length > 0;
});

const updateFilter = (name: string, value: any) => {
  const newFilters = { ...props.modelValue };
  newFilters[name] = value;
  emit('update:modelValue', newFilters);
};

const clearFilters = () => {
  const clearedFilters: Record<string, any> = {};
  props.filters.forEach(filter => {
    clearedFilters[filter.name] = '';
  });
  emit('update:modelValue', clearedFilters);
  emit('clear');
};

const getFilterLabel = (filterName: string): string => {
  const filter = props.filters.find(f => f.name === filterName);
  return filter?.label || filterName;
};

const getFilterValueLabel = (filterName: string, value: any): string => {
  const filter = props.filters.find(f => f.name === filterName);
  
  if (filter?.type === 'select' && filter.options) {
    const option = filter.options.find(opt => opt.value === value);
    return option?.label || value;
  }
  
  return String(value);
};
</script>

<style scoped>
.filter-bar {
  @apply bg-theme-surface border border-theme-border rounded-lg p-4 space-y-4;
}

.filter-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-end;
}

.filter-field {
  @apply space-y-1;
}

.filter-label {
  @apply block text-sm font-medium text-theme-primary;
}

.filter-select,
.filter-input {
  @apply w-full px-3 py-2 border border-theme-border bg-theme-surface text-theme-primary rounded-lg focus:outline-none focus:ring-2 focus:ring-theme-primary focus:border-transparent text-sm;
}

.filter-actions {
  @apply flex items-center justify-center;
}

.btn-clear {
  @apply flex items-center gap-2 px-3 py-2 text-sm font-medium text-theme-muted hover:text-theme-primary hover:bg-theme-surface-light rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.active-filters {
  @apply pt-4 border-t border-theme-border;
}

.active-filter-tag {
  @apply flex items-center gap-1 px-2 py-1 bg-theme-primary bg-opacity-10 text-theme-primary rounded-full text-xs;
}

.active-filter-remove {
  @apply text-theme-primary hover:text-red-600 transition-colors;
}

/* Mobile responsive */
@media (max-width: 640px) {
  .filter-grid {
    @apply grid-cols-1;
  }
  
  .filter-actions {
    @apply justify-start;
  }
  
  .active-filters .flex {
    @apply flex-col items-start gap-2;
  }
}
</style>
