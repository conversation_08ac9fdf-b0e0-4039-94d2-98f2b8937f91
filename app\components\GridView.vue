<template>
  <div class="space-y-6">
    <!-- Section Header -->
    <div class="flex items-center justify-between">
      <h2 class="text-2xl font-bold section-title">{{ title }}</h2>
      <NuxtLink
        v-if="showViewAll"
        :to="viewAllLink"
        class="text-theme-accent hover:text-theme-primary text-sm font-medium transition-colors"
      >
        عرض الكل ←
      </NuxtLink>
    </div>

    <!-- Grid Layout -->
    <div :class="gridClasses">
      <!-- Product Cards -->
      <template v-if="type === 'products'">
        <ProductCard 
          v-for="item in displayItems" 
          :key="item.id" 
          :product="item" 
        />
      </template>

      <!-- Category Cards -->
      <template v-else-if="type === 'categories'">
        <NuxtLink 
          v-for="item in displayItems" 
          :key="item.id" 
          :to="`/shop?category=${item.slug}`"
        >
          <div class="product-card h-full flex flex-col group">
            <!-- Category Image - Square aspect ratio (1:1) -->
            <div class="relative aspect-square overflow-hidden">
              <img
                :src="item.image || '/logo.jpg'"
                :alt="item.name"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              
              <!-- Overlay gradient -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
              
              <!-- Product count badge -->
              <div class="absolute bottom-2 left-2 bg-theme-glass text-theme-primary px-2 py-1 rounded-lg text-xs">
                {{ item.productCount }} منتج
              </div>
            </div>

            <!-- Category Info -->
            <div class="p-3 flex flex-col justify-center bg-theme-surface min-h-[60px]">
              <!-- Category Name -->
              <h3 class="text-sm font-semibold text-white-force text-center">
                {{ item.name }}
              </h3>
            </div>
          </div>
        </NuxtLink>
      </template>
    </div>

    <!-- Load More Button (if needed) -->
    <div v-if="showLoadMore && hasMore" class="text-center">
      <button
        @click="loadMore"
        class="btn-primary"
      >
        تحميل المزيد
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Product, Category } from '~/data/mockData'

interface Props {
  items: Product[] | Category[]
  type: 'products' | 'categories'
  title: string
  limit?: number
  showViewAll?: boolean
  viewAllLink?: string
  showLoadMore?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  limit: 6,
  showViewAll: true,
  viewAllLink: '/shop',
  showLoadMore: false,
})

const currentLimit = ref(props.limit)

const displayItems = computed(() => {
  return props.items.slice(0, currentLimit.value)
})

const hasMore = computed(() => {
  return props.items.length > currentLimit.value
})

const gridClasses = computed(() => {
  if (props.type === 'categories') {
    return 'grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 lg:gap-6'
  }
  return 'grid grid-cols-3 gap-3 md:gap-4 lg:gap-6'
})

const loadMore = () => {
  currentLimit.value += props.limit
}
</script>
