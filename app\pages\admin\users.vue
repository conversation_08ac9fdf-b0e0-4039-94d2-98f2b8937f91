<template>
  <div>
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Users Management</h1>
        <p class="text-lg text-gray-600">Manage user accounts and permissions</p>
      </div>
      <button 
        @click="showCreateModal = true"
        class="btn-primary"
      >
        Add New User
      </button>
    </div>

    <!-- User Filters -->
    <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <input 
            v-model="searchQuery"
            type="text" 
            placeholder="Search users..."
            class="input-field"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
          <select v-model="roleFilter" class="input-field">
            <option value="">All Roles</option>
            <option value="admin">Admin</option>
            <option value="worker">Worker</option>
            <option value="distributor">Distributor</option>
            <option value="user">Customer</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select v-model="statusFilter" class="input-field">
            <option value="">All Users</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </select>
        </div>

        <div class="flex items-end">
          <button 
            @click="clearFilters"
            class="btn-secondary text-sm w-full"
          >
            Clear Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Users Table -->
    <div class="card overflow-hidden">
      <div v-if="pending" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading users...</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Joined
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Active
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="user in filteredUsers" :key="user.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-gray-700">
                      {{ getUserInitials(user) }}
                    </span>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      {{ user.firstName }} {{ user.lastName }}
                    </div>
                    <div class="text-sm text-gray-500">{{ user.email }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getRoleClass(user.role)" class="badge">
                  {{ user.role }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(user.status)" class="badge">
                  {{ user.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(user.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(user.lastActiveAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button 
                  @click="editUser(user)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  Edit
                </button>
                <button 
                  v-if="user.status === 'active'"
                  @click="suspendUser(user.id)"
                  class="text-yellow-600 hover:text-yellow-900"
                >
                  Suspend
                </button>
                <button 
                  v-else-if="user.status === 'suspended'"
                  @click="activateUser(user.id)"
                  class="text-green-600 hover:text-green-900"
                >
                  Activate
                </button>
                <button 
                  @click="deleteUser(user.id)"
                  class="text-red-600 hover:text-red-900"
                >
                  Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-if="filteredUsers.length === 0" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No users found</h3>
          <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
        </div>
      </div>
    </div>

    <!-- Create/Edit User Modal -->
    <UserModal 
      v-if="showCreateModal || showEditModal"
      :user="editingUser"
      :is-editing="showEditModal"
      @close="closeModals"
      @success="handleUserSuccess"
    />
  </div>
</template>

<script setup lang="ts">
// Layout
definePageMeta({
  layout: 'dashboard',
  middleware: 'admin'
});

// SEO Meta
useHead({
  title: 'Users Management - Admin Dashboard',
  meta: [
    { name: 'description', content: 'Manage user accounts, roles, and permissions.' }
  ]
});

// Data fetching - TODO: backend - Replace with real users API
const { data: users, pending, refresh } = await useAsyncData('admin-users', () => 
  $fetch('/backend/api/users')
);

// Filters
const searchQuery = ref('');
const roleFilter = ref('');
const statusFilter = ref('');

// Modals
const showCreateModal = ref(false);
const showEditModal = ref(false);
const editingUser = ref(null);

// Computed properties
const filteredUsers = computed(() => {
  let filtered = users.value?.data || [];

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter((user: any) => 
      user.firstName.toLowerCase().includes(query) ||
      user.lastName.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query)
    );
  }

  // Role filter
  if (roleFilter.value) {
    filtered = filtered.filter((user: any) => user.role === roleFilter.value);
  }

  // Status filter
  if (statusFilter.value) {
    filtered = filtered.filter((user: any) => user.status === statusFilter.value);
  }

  // Sort by creation date (newest first)
  return filtered.sort((a: any, b: any) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
});

// Methods
const getUserInitials = (user: any) => {
  return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
};

const getRoleClass = (role: string) => {
  const roleClasses = {
    admin: 'badge-admin',
    worker: 'badge-worker',
    distributor: 'badge-distributor',
    user: 'badge-user'
  };
  return roleClasses[role as keyof typeof roleClasses] || 'badge-user';
};

const getStatusClass = (status: string) => {
  const statusClasses = {
    active: 'status-completed',
    inactive: 'bg-gray-100 text-gray-800',
    suspended: 'bg-red-100 text-red-800'
  };
  return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800';
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const clearFilters = () => {
  searchQuery.value = '';
  roleFilter.value = '';
  statusFilter.value = '';
};

const editUser = (user: any) => {
  editingUser.value = user;
  showEditModal.value = true;
};

const suspendUser = async (userId: number) => {
  if (confirm('Are you sure you want to suspend this user?')) {
    // TODO: backend - Implement user suspension API
    console.log('Suspending user:', userId);
    await refresh();
  }
};

const activateUser = async (userId: number) => {
  // TODO: backend - Implement user activation API
  console.log('Activating user:', userId);
  await refresh();
};

const deleteUser = async (userId: number) => {
  if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
    // TODO: backend - Implement user deletion API
    console.log('Deleting user:', userId);
    await refresh();
  }
};

const closeModals = () => {
  showCreateModal.value = false;
  showEditModal.value = false;
  editingUser.value = null;
};

const handleUserSuccess = async () => {
  closeModals();
  await refresh();
};
</script>
