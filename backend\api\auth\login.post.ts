// TODO: backend - Replace with real authentication system
export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const { email, password } = body;

  // Mock authentication - DO NOT use in production
  const mockUsers = [
    { id: 1, email: "<EMAIL>", password: "admin123", role: "admin", name: "<PERSON>" },
    { id: 2, email: "<EMAIL>", password: "worker123", role: "worker", name: "<PERSON>" },
    { id: 3, email: "<EMAIL>", password: "dist123", role: "distributor", name: "<PERSON> Distributor" },
    { id: 4, email: "<EMAIL>", password: "user123", role: "user", name: "<PERSON> Customer" }
  ];

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));

  const user = mockUsers.find(u => u.email === email && u.password === password);

  if (!user) {
    throw createError({
      statusCode: 401,
      statusMessage: "Invalid credentials"
    });
  }

  // Mock JWT token (in real app, use proper JWT)
  const token = `mock-jwt-${user.id}-${Date.now()}`;

  return {
    success: true,
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      },
      token
    }
  };
});
