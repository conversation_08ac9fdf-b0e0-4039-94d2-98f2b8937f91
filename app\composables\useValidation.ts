/**
 * Form validation composable
 */

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  email?: boolean;
  phone?: boolean;
  url?: boolean;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationError {
  field: string;
  message: string;
}

export const useValidation = () => {
  const { isValidEmail, isValidPhone } = useUtils();

  /**
   * Validate a single field
   */
  const validateField = (value: any, rules: ValidationRule, fieldName: string = 'Field'): string | null => {
    // Required validation
    if (rules.required && (value === null || value === undefined || value === '')) {
      return `${fieldName} is required`;
    }

    // Skip other validations if value is empty and not required
    if (!rules.required && (value === null || value === undefined || value === '')) {
      return null;
    }

    // String validations
    if (typeof value === 'string') {
      // Min length validation
      if (rules.minLength && value.length < rules.minLength) {
        return `${fieldName} must be at least ${rules.minLength} characters long`;
      }

      // Max length validation
      if (rules.maxLength && value.length > rules.maxLength) {
        return `${fieldName} must be no more than ${rules.maxLength} characters long`;
      }

      // Email validation
      if (rules.email && !isValidEmail(value)) {
        return `${fieldName} must be a valid email address`;
      }

      // Phone validation
      if (rules.phone && !isValidPhone(value)) {
        return `${fieldName} must be a valid phone number`;
      }

      // URL validation
      if (rules.url) {
        try {
          new URL(value);
        } catch {
          return `${fieldName} must be a valid URL`;
        }
      }

      // Pattern validation
      if (rules.pattern && !rules.pattern.test(value)) {
        return `${fieldName} format is invalid`;
      }
    }

    // Number validations
    if (typeof value === 'number') {
      // Min value validation
      if (rules.min !== undefined && value < rules.min) {
        return `${fieldName} must be at least ${rules.min}`;
      }

      // Max value validation
      if (rules.max !== undefined && value > rules.max) {
        return `${fieldName} must be no more than ${rules.max}`;
      }
    }

    // Custom validation
    if (rules.custom) {
      const customError = rules.custom(value);
      if (customError) {
        return customError;
      }
    }

    return null;
  };

  /**
   * Validate an entire form
   */
  const validateForm = (
    data: Record<string, any>,
    rules: Record<string, ValidationRule>,
    fieldNames?: Record<string, string>
  ): ValidationError[] => {
    const errors: ValidationError[] = [];

    for (const [field, fieldRules] of Object.entries(rules)) {
      const value = data[field];
      const displayName = fieldNames?.[field] || field;
      const error = validateField(value, fieldRules, displayName);

      if (error) {
        errors.push({ field, message: error });
      }
    }

    return errors;
  };

  /**
   * Create a reactive form validator
   */
  const createValidator = (
    initialData: Record<string, any>,
    rules: Record<string, ValidationRule>,
    fieldNames?: Record<string, string>
  ) => {
    const data = reactive({ ...initialData });
    const errors = reactive<Record<string, string>>({});
    const isValid = computed(() => Object.keys(errors).length === 0);

    /**
     * Validate a specific field
     */
    const validateSingleField = (field: string) => {
      const value = data[field];
      const fieldRules = rules[field];
      const displayName = fieldNames?.[field] || field;

      if (fieldRules) {
        const error = validateField(value, fieldRules, displayName);
        if (error) {
          errors[field] = error;
        } else {
          delete errors[field];
        }
      }
    };

    /**
     * Validate all fields
     */
    const validateAll = (): boolean => {
      // Clear existing errors
      Object.keys(errors).forEach(key => delete errors[key]);

      // Validate all fields
      const validationErrors = validateForm(data, rules, fieldNames);
      validationErrors.forEach(error => {
        errors[error.field] = error.message;
      });

      return validationErrors.length === 0;
    };

    /**
     * Clear all errors
     */
    const clearErrors = () => {
      Object.keys(errors).forEach(key => delete errors[key]);
    };

    /**
     * Clear error for a specific field
     */
    const clearFieldError = (field: string) => {
      delete errors[field];
    };

    /**
     * Set custom error for a field
     */
    const setFieldError = (field: string, message: string) => {
      errors[field] = message;
    };

    /**
     * Reset form to initial state
     */
    const reset = () => {
      Object.assign(data, initialData);
      clearErrors();
    };

    return {
      data,
      errors: readonly(errors),
      isValid,
      validateField: validateSingleField,
      validateAll,
      clearErrors,
      clearFieldError,
      setFieldError,
      reset
    };
  };

  /**
   * Common validation rules
   */
  const commonRules = {
    required: { required: true },
    email: { required: true, email: true },
    phone: { phone: true },
    url: { url: true },
    password: { required: true, minLength: 8 },
    name: { required: true, minLength: 2, maxLength: 50 },
    description: { maxLength: 500 },
    price: { required: true, min: 0 },
    quantity: { required: true, min: 1 },
    positiveNumber: { min: 0 },
    percentage: { min: 0, max: 100 }
  };

  /**
   * Password confirmation validator
   */
  const passwordConfirmation = (password: string) => ({
    custom: (value: string) => {
      if (value !== password) {
        return 'Passwords do not match';
      }
      return null;
    }
  });

  /**
   * Unique value validator (for arrays)
   */
  const uniqueInArray = (array: any[], currentIndex?: number) => ({
    custom: (value: any) => {
      const duplicateIndex = array.findIndex((item, index) => 
        item === value && index !== currentIndex
      );
      if (duplicateIndex !== -1) {
        return 'This value must be unique';
      }
      return null;
    }
  });

  /**
   * Date range validator
   */
  const dateRange = (minDate?: Date, maxDate?: Date) => ({
    custom: (value: string | Date) => {
      const date = typeof value === 'string' ? new Date(value) : value;
      
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      
      if (minDate && date < minDate) {
        return `Date must be after ${minDate.toLocaleDateString()}`;
      }
      
      if (maxDate && date > maxDate) {
        return `Date must be before ${maxDate.toLocaleDateString()}`;
      }
      
      return null;
    }
  });

  return {
    validateField,
    validateForm,
    createValidator,
    commonRules,
    passwordConfirmation,
    uniqueInArray,
    dateRange
  };
};
